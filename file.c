#include <errno.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <stdarg.h>
#include <sys/time.h>
#include <time.h>
#include <termios.h> /*PPSIX 终端控制定义*/
#include "file.h"
#include "manage.h"

/*
 * @description       : 要存文件的字符串按照格式放到带来的字符串指针里
 * @param - *destpoint: 用于存放格式化好的字符串
 * @param - *fmt, ... ：格式化方式
 * @return		      : 字符串长度
 */
int Func_Dprintf(char *destpoint, char *fmt, ...)
{
    va_list arg_ptr;
    char uLen, *tmpBuf;

    tmpBuf = destpoint;
    va_start(arg_ptr, fmt);
    uLen = vsprintf(tmpBuf, fmt, arg_ptr);

    va_end(arg_ptr); // 用va_end宏结束可变参数的获取
    return uLen;
}
/*
 * @description    : 给文件补全路径
 * @param - *dest  ：补全路径后的文件名
 * @param - *src   ：当前路径
 * @param - *name  ：原文件名
 * @return		   : 无
 */
void file_add_full_fath(char *dest, char *src, char *name)
{
    int len1 = 0, len2;

    memset(dest, 0, strlen(dest));
    len1 = strlen(src);
    memcpy(dest, src, len1);
    len2 = strlen(name);
    memcpy(dest + len1, name, len2);
}
/*
 * @description      : 获取当前时间，并转为字符串，放到带来的字符串指针里
 * @param - *TimeChar: 用于存放时间字符串
 * @return		     : 时间字符串长度
 */
/*
int Func_Time_GetSystemTime_ToChar(char *TimeChar)
{
    // time_t Second = 0;
    struct timeval Time;
    struct tm *pSystime = NULL;
    int TimeLen = 0;
    char TempBuf[50];

    gettimeofday(&Time, NULL);
    pSystime = localtime(&Time.tv_sec);
    memset(TempBuf, 0, sizeof(TempBuf));
    strcat(TempBuf, "%04d-%02d-%02d %02d:%02d:%02d");
    TimeLen = Func_Dprintf(TimeChar, TempBuf, pSystime->tm_year + 1900, pSystime->tm_mon + 1,
                           pSystime->tm_mday, pSystime->tm_hour, pSystime->tm_min, pSystime->tm_sec);

    return TimeLen;
}*/
int Func_Time_GetSystemTime_ToChar(char *TimeChar)
{
    struct timespec time = {0, 0};
    int TimeLen = 0;
    float s_f = 0.0f;

    clock_gettime(CLOCK_MONOTONIC, &time);
    s_f = time.tv_nsec * 1.0f;
    s_f /= 1000000000.0f;
    TimeLen = sprintf(TimeChar, "[%12.6f]", ((float)(time.tv_sec * 1.0f) + s_f));
    return TimeLen;
}

/*
 * @description : 打开文件
 * @param - path: 文件路径
 * @return		: 文件结构指针
 */
FILE *file_open(char *path, enum_file_mode mode)
{
    FILE *pfile = NULL;

    if (ATWR == mode)
        pfile = fopen(path, "at+");
    else if (WTWR == mode)
        pfile = fopen(path, "wt+");
    else
        pfile = fopen(path, "r");
    // 为文件设置FD_CLOEXEC标志，防止子进程继承
    if (pfile != NULL)
    {
        int fd = fileno(pfile);
        if (fd != -1)
        {
            int flags = fcntl(fd, F_GETFD);
            if (flags != -1)
            {
                fcntl(fd, F_SETFD, flags | FD_CLOEXEC);
            }
        }
    }
    /*
    if (pfile == NULL)
    {
        printf("File Open Fail!\n");
    }
    else
        printf("File Open %s OK!\n", path);*/
    return pfile;
}

/*
 * @description    : 写文件
 * @param - pfile  : 文件结构指针
 * @param - scr_ata: 要写入文件的数据
 * @param - len    : 要写入文件数据长度
 * @return		   : 无
 */
void file_write_data(FILE *pfile, char *scr_ata, int len)
{
    fwrite(scr_ata, sizeof(char), len, pfile); /*写入数据*/

    sync_len += len;
    if (sync_len > MAX_SYNC_LEN)
    {
        fflush(pfile); /*将数据同步至ROM*/
        sync_len = 0;
    }
}
/*
 * @description    : 写文件，带时间戳
 * @param - pfile  : 文件结构指针
 * @param - scr_ata: 要写入文件的数据
 * @param - len    : 要写入文件数据长度
 * @return		   : 无
 */
void file_write_time_data(FILE *pfile, char *scr_ata, int len)
{
    char Time[50] = {0};
    int time_len = 0;

    time_len = Func_Time_GetSystemTime_ToChar(Time);
    fwrite(Time, sizeof(char), time_len, pfile); // 写入时间
    fwrite(scr_ata, sizeof(char), len, pfile);   /*写入数据*/

    sync_len += len;
    if (sync_len > MAX_SYNC_LEN)
    {
        fflush(pfile); /*将数据同步至ROM*/
        sync_len = 0;
    }
}
/*
 * @description    : 写格式化内容到文件，内容无时间戳
 * @param - pfile  : 文件结构指针
 * @param - *fmt   : 日志内容
 * @return		   : 无
 */
void file_write_fmt(FILE *pfile, char *fmt, ...)
{
    va_list arg_ptr;
    char buffer[500] = {0};
    int uLen = 0;

    va_start(arg_ptr, fmt);
    uLen = vsprintf(buffer, fmt, arg_ptr);
    va_end(arg_ptr); // 用va_end宏结束可变参数的获取

    fwrite(buffer, sizeof(char), uLen, pfile); /*写入数据*/
    sync_len += uLen;
    if (sync_len > MAX_SYNC_LEN)
    {
        fflush(pfile); /*将数据同步至ROM*/
        sync_len = 0;
    }
}
/*
 * @description    : 写log日志，内容前增加时间戳
 * @param - pfile  : 文件结构指针
 * @param - *fmt   : 日志内容
 * @return		   : 无
 */
void file_write_time_fmt(FILE *pfile, char *fmt, ...)
{
    va_list arg_ptr;
    char buffer[500] = {0};
    char Time[50] = {0};
    int uLen = 0, time_len = 0;

    va_start(arg_ptr, fmt);
    uLen = vsprintf(buffer, fmt, arg_ptr);
    va_end(arg_ptr); // 用va_end宏结束可变参数的获取

    time_len = Func_Time_GetSystemTime_ToChar(Time);
    fwrite(Time, sizeof(char), time_len, pfile); // 写入时间
    fwrite(buffer, sizeof(char), uLen, pfile);   /*写入数据*/

    sync_len += time_len + uLen;
    if (sync_len > MAX_SYNC_LEN)
    {
        fflush(pfile); /*将数据同步至ROM*/
        sync_len = 0;
    }
}

/*
 * @description     : 将总执行轮次写入文件
 * @param - *pfile  : 文件结构指针
 * @param - *infos  : 测试信息
 * @return		    : 无
 */
void file_write_head(FILE *pfile, struct_test_info *infos)
{
    char Time[50] = {0};
    char data[200] = {0};
    int len = 0;
    int Timelen = 0;

    fwrite("\n**************************************************\n", sizeof(char), strlen("\n**************************************************\n"), pfile); /*写入数据*/
    printf("\n**************************************************\n");
    len = Func_Dprintf(data, "*** starts times is %d , whole execute times is %d ***\n", infos->run_count, infos->circle_count);
    if (len >= sizeof(data))
        len = sizeof(data) - 1;
    fwrite(data, sizeof(char), len, pfile); /*写入数据*/
    printf("%s", data);
    memset(data, 0, sizeof(data));
    fwrite("** ", sizeof(char), strlen("** "), pfile); /*写入数据*/
    Timelen = Func_Time_GetSystemTime_ToChar(Time);
    fwrite(Time, sizeof(char), Timelen, pfile); // 写入时间
    fwrite(" **\n", sizeof(char), strlen(" **\n"), pfile);
    fwrite("**************************************************\n", sizeof(char), strlen("**************************************************\n"), pfile); /*写入数据*/
    printf("** %s **\n", Time);
    printf("**************************************************\n");

    fflush(pfile); /*将数据同步至ROM*/
}
/*
 * @description     : 分项测试轮次写入文件
 * @param - pfile   : 文件结构指针
 * @param - *infos  : 测试信息
 * @param - *item   : 测试条目信息
 * @param - current : 当前测试号
 * @return		    : 无
 */
void file_write_subitem_head(FILE *pfile, struct_test_info *infos, struct_test_item *item)
{
    char Time[50] = {0};
    char data[200] = {0};
    char name[10] = {0};
    int len = 0;
    int Timelen = 0;

    get_test_name(item->numb, name);
    fwrite("\n**************************************************\n", sizeof(char), strlen("\n**************************************************\n"), pfile); /*写入数据*/
    printf("\n**************************************************\n");
    len = Func_Dprintf(data, "*** starts times is %d , whole execute times is %d ,test name is %s , item execute times is %d. ***\n",
                       infos->run_count, infos->circle_count, name, (item->count + 1));
    if (len >= sizeof(data))
        len = sizeof(data) - 1;
    fwrite(data, sizeof(char), len, pfile); /*写入数据*/
    printf("%s", data);
    memset(data, 0, sizeof(data));
    fwrite("** ", sizeof(char), strlen("** "), pfile); /*写入数据*/
    Timelen = Func_Time_GetSystemTime_ToChar(Time);
    fwrite(Time, sizeof(char), Timelen, pfile);                                                                                                          // 写入时间
    fwrite(" **\n", sizeof(char), strlen(" **\n"), pfile);                                                                                               /*写入数据*/
    fwrite("**************************************************\n", sizeof(char), strlen("**************************************************\n"), pfile); /*写入数据*/
    printf("** %s **\n", Time);
    printf("**************************************************\n");

    fflush(pfile); /*将数据同步至ROM*/
}

/*
 * @description     : 分项测试轮次写入文件
 * @param - pfile   : 文件结构指针
 * @param - *infos  : 测试信息
 * @param - *item   : 测试条目信息
 * @param - current : 当前测试号
 * @return		    : 无
 */
void file_write_infos(FILE *pfile, struct_test_info *infos)
{
    char time[50] = {0};
    char data[300] = {0};

    // 获取温度值
    get_temperature_info(test->temp_path, &info.temperature, test->temp_coef);
    // 获取频率值
    get_freq_info(test->freq_path, &info.freq);
    // 获取CPU占用率
    get_cpu_employ("/proc/stat", &info.cpu_employ);
    // 获取内存占用率
    get_mem_employ("/proc/meminfo", &info.mem_employ, NULL);
    // 获取读写速率
    // get_tps_r_w(info.dev, &info.tps, &info.kB_read, &info.kB_wrtn);

    Func_Time_GetSystemTime_ToChar(time);
    // printf("\n%s temperature : %d , freq : %d MHz , cpu_employ : %.3f%% , mem_employ : %.3f%% , tps : %.2f , kB_read/s : %.2f , kB_wrtn/s : %.2f\n", time, info.temperature, info.freq, info.cpu_employ, info.mem_employ, info.tps, info.kB_read, info.kB_wrtn);
    printf("\n%s temperature : %d , freq : %d MHz , cpu_employ : %.3f%% , mem_employ : %.3f%% \n", time, info.temperature, info.freq, info.cpu_employ, info.mem_employ);
    memset(data, 0, sizeof(data));
    sprintf(data, "%s,%d,%d,%.3f,%.3f\n", time, info.temperature, info.freq, info.cpu_employ, info.mem_employ);
    fwrite(data, sizeof(char), strlen(data), pfile);
    fflush(pfile); /*将数据同步至ROM*/
}
/*
 * @description        : 每分钟存储历史数据
 * @param - *pfile     : 文件指针
 * @param - *manag     : 测试管理结构体·
 * @param - flag       : 0-写表头，1-写数据
 * @return		       : 无
 */
void save_data_sheet(FILE *pfile, struct_test *manag, int flag)
{
    int i = 0, j = 0, k = 0;
    char name[10] = {0};
    char temp_str[150] = {0};

    if (0 == flag)
    {
        fwrite("time", sizeof(char), strlen("time"), pfile);
        for (i = 0; i < MAX_TEST_ITEM; i++)
        {
            if (manag->item[i].circle)
            {
                for (j = 0; j < manag->item[i].groups; j++)
                {
                    for (k = 0; k < manag->item[i].group[j].numb; k++)
                    {
                        memset(name, 0, sizeof(name));
                        get_test_name(i, name);
                        switch (manag->item[i].group[j].cmd[k].exe_param.mode)
                        {
                        case DD_MODE: //
                        {
                            memset(temp_str, 0, sizeof(temp_str));
                            sprintf(temp_str, ",%s_dd_%s_write,%s_dd_%s_read", name, manag->item[i].group[j].cmd[k].exe_param.dd_path, name, manag->item[i].group[j].cmd[k].exe_param.dd_path);
                            fwrite(temp_str, sizeof(char), strlen(temp_str), pfile);
                        }
                        break;
                        case NET_MODE: //
                        {
                            memset(temp_str, 0, sizeof(temp_str));
                            sprintf(temp_str, ",%s_iperf3_net%d_%d_speed", name, manag->item[i].group[j].cmd[k].exe_param.net_num, manag->item[i].group[j].cmd[k].exe_param.net_flag);
                            fwrite(temp_str, sizeof(char), strlen(temp_str), pfile);
                        }
                        break;
                        default:
                        {
                            break;
                        }
                        }
                    }
                }
            }
        }
        fwrite("\n", sizeof(char), strlen("\n"), pfile);
    }
    else
    {
        Func_Time_GetSystemTime_ToChar(temp_str);
        fwrite(temp_str, sizeof(char), strlen(temp_str), pfile);
        for (i = 0; i < MAX_TEST_ITEM; i++)
        {
            if (manag->item[i].circle)
            {
                for (j = 0; j < manag->item[i].groups; j++)
                {
                    for (k = 0; k < manag->item[i].group[j].numb; k++)
                    {
                        memset(name, 0, sizeof(name));
                        get_test_name(i, name);
                        switch (manag->item[i].group[j].cmd[k].exe_param.mode)
                        {
                        case DD_MODE: //
                        {
                            memset(temp_str, 0, sizeof(temp_str));
                            if (manag->item[i].group[j].cmd[k].new_data_flag == 1)
                                sprintf(temp_str, ",%.2f,%.2f", manag->item[i].group[j].cmd[k].result_data[0].value, manag->item[i].group[j].cmd[k].result_data[1].value);
                            else
                                sprintf(temp_str, ",%.2f,%.2f", 0.0f, 0.0f);
                            fwrite(temp_str, sizeof(char), strlen(temp_str), pfile);
                            manag->item[i].group[j].cmd[k].new_data_flag = 0;
                        }
                        break;
                        case NET_MODE: //
                        {
                            memset(temp_str, 0, sizeof(temp_str));
                            if (manag->item[i].group[j].cmd[k].new_data_flag == 1)
                                sprintf(temp_str, ",%.2f", manag->item[i].group[j].cmd[k].result_data[0].value);
                            else
                                sprintf(temp_str, ",%.2f", 0.0f);
                            fwrite(temp_str, sizeof(char), strlen(temp_str), pfile);
                            manag->item[i].group[j].cmd[k].new_data_flag = 0;
                        }
                        break;
                        default:
                        {
                            break;
                        }
                        }
                    }
                }
            }
        }
        fwrite("\n", sizeof(char), strlen("\n"), pfile);
    }
}
/*
 * @description     : 读文件
 * @param - *pfile  : 文件结构指针
 * @param - *scr_ata: 要读入文件的数据缓冲区首地址
 * @param - len     : 要读入文件数据最大长度
 * @param - location: 指定文件读入位置
 * @return		    : 读入文件实际长度
 */
int file_read(FILE *pfile, char *scr_ata, int len, long location)
{
    int read_len = 0;

    fseek(pfile, location, SEEK_SET);
    read_len = fread(scr_ata, sizeof(char), len, pfile); /*读出数据*/

    return read_len;
}
/*
 * @description     : 读文件最后多少字节
 * @param - *pfile  : 文件结构指针
 * @param - *scr_ata: 要读入文件的数据缓冲区首地址
 * @param - len     : 要读入文件数据最大长度
 * @param - location: 指定文件读入位置
 * @return		    : 读入文件实际长度
 */
int file_read_last_n_data(FILE *pfile, char *scr_ata, int len, int location)
{
    int read_len = 0;

    if (0 == fseek(pfile, location, SEEK_END))
    {
        read_len = fread(scr_ata, sizeof(char), len, pfile); /*读出数据*/
    }
    else
        read_len = -1;

    return read_len;
}
/*
 * @description    : 关闭文件
 * @return		   : 无
 */
void file_close(FILE *pfile)
{
    fclose(pfile);
}
/*
 * @description    : 将文件2数据追加到文件1最后。
 * @param - *des   : 目标文件1
 * @param - *scr   : 原文件2
 * @return		   : 无
 */
void file_add(FILE *des, FILE *scr)
{
    char temp_data[500] = {0};
    int data_len = 0, i = 0;

    // lseek(des, 0, SEEK_END);
    // lseek(scr, 0, SEEK_SET);
    data_len = file_read(scr, temp_data, 500, 0);
    while (data_len)
    {
        file_write_data(des, temp_data, data_len);
        i++;
        data_len = file_read(scr, temp_data, 500, (i * 500));
    }
    fflush(des); /*将数据同步至ROM*/
}

/* CRC-32 多项式表 */
static unsigned int crc32_table[256];

/**
 * @description    : 初始化CRC-32表
 * @return         : 无
 */
void init_crc32_table(void)
{
    unsigned int c;
    int i, j;

    for (i = 0; i < 256; i++)
    {
        c = (unsigned int)i;
        for (j = 0; j < 8; j++)
        {
            if (c & 1)
                c = 0xEDB88320 ^ (c >> 1);
            else
                c = c >> 1;
        }
        crc32_table[i] = c;
    }
}

/**
 * @description    : 计算缓冲区的CRC-32值
 * @param - buf    : 数据缓冲区
 * @param - len    : 缓冲区长度
 * @param - crc    : 初始CRC值
 * @return         : 计算得到的CRC-32值
 */
unsigned int calculate_crc32(const unsigned char *buf, size_t len, unsigned int crc)
{
    unsigned int i;

    crc = ~crc;
    for (i = 0; i < len; i++)
    {
        crc = crc32_table[(crc ^ buf[i]) & 0xFF] ^ (crc >> 8);
    }
    return ~crc;
}

/**
 * @description    : 计算文件的CRC-32值
 * @param - path   : 文件路径
 * @return         : 成功返回CRC-32值，失败返回0
 */
unsigned int Func_get_conf_crc(const char *path)
{
    FILE *file;
    unsigned char buffer[1024];
    size_t bytes_read;
    unsigned int crc = 0;

    /* 打开文件 */
    file = fopen(path, "rb");
    if (file == NULL)
    {
        printf("can not open file: %s\n", path);
        return 0;
    }

    /* 初始化CRC表 */
    init_crc32_table();

    /* 初始化CRC值 */
    crc = 0;

    /* 读取文件内容并计算CRC */
    while ((bytes_read = fread(buffer, 1, sizeof(buffer), file)) > 0)
    {
        crc = calculate_crc32(buffer, bytes_read, crc);
    }

    /* 关闭文件 */
    fclose(file);

    return crc;
}
