
#ifndef MANAGE_H
#define MANAGE_H

#define MAX_TEST_ITEM 24
#define MAX_EXE_ITEM 10
#define MAX_GROUP 30

#define MAX_DEVEICE_NUMB 50
#define MAX_CONTENT_LEN 200
#define MAX_INFO_LEN 300
#define MAX_FREQ_NUMB 10
#define MAX_VALUE_NUMB 10
#define MAX_PRE_CMD_NUMB 20

#define TEST_NAME_LEN 20
#define ERR_KEYWORD_LEN 50
#define TIMOUT_TIME 1000 * 60 * 10
#define MAX_PATH_LEN 100
#define MAX_STAGE 5

#define SPONSOR 0
#define RESPONSE 1

#define IDLE 0
#define RUNNING 1
#define OVER 2
#define WAITING 3
#define NOTHING_TO_DO 4

#define CURRENT_STATE_RUN 1
#define CURRENT_STATE_OVER 2

#define QUITE_EVENT 1
#define PID_ERR_MAX 2

#define READ 0
#define WRITE 1

/*测试项枚举*/
typedef enum
{
    TYPE_NO = 0,  /*未知*/
    SOC = 1,      /*cpu*/
    DDR = 2,      /*ddr*/
    EMMC = 3,     /*emmc*/
    NAND = 4,     /*nand*/
    SPI = 5,      /*spi*/
    IIC = 6,      /*i2c*/
    SD = 7,       /*sd卡*/
    USB = 8,      /*usb*/
    TYPE_C = 9,   /*TYPE-C*/
    SATA = 10,    /*sata*/
    CAN = 11,     /*can*/
    RS485 = 12,   /*rs485*/
    RS232 = 13,   /*rs232*/
    NET = 14,     /*网络*/
    AUDIO = 15,   /*音频*/
    CAMERA = 16,  /*摄像头*/
    RTC = 17,     /*rtc*/
    G4 = 18,      /*4G*/
    WIFI = 19,    /*WIFI*/
    DIDO = 20,    /*IO口*/
    PCIE = 21,    /*PCIE*/
    BT = 22,      /*蓝牙*/
    DISPLAY = 23, /*显示*/

} enum_test_type;
/*模式枚举*/
typedef enum
{
    ANY_MODE = 0,       /*任意模式*/
    DD_MODE = 1,        /*DD测试*/
    MEMTER_MODE = 2,    /*memtester测试*/
    MEM_BW_MODE = 3,    /*内存带宽测试*/
    NET_MODE = 4,       /*网络测试*/
    RTC_TEST_MODE = 5,  /*RTC测试*/
    CHECK_NAND_ERR = 6, /*检查NAND变位计数*/
} enum_test_mode;

typedef enum
{
    SPONSOR_IN_ORDER = 0,    /*被测试设备，顺序执行*/
    SPONSOR_CONCURRENT = 1,  /*被测试设备，并行执行*/
    RESPONSE_IN_ORDER = 2,   /*响应测试设备，顺序执行*/
    RESPONSE_CONCURRENT = 3, /*响应测试设备，并行执行*/
} enum_pattern_type;

/*测试参数枚举*/
typedef enum
{
    UNKNOW_PARA = 0, /*未知参数*/
    MEM = 1,         /*MEM大小*/

} enum_test_para;

/*测试项字符串和枚举对应*/
typedef struct
{
    enum_test_type numbs;    /*AT类别*/
    char str[TEST_NAME_LEN]; /*字符串内容*/
} struct_name_str2numb;

/*测试参数字符串转枚举*/
typedef struct
{
    enum_test_para numbs; /*读取内容*/
    char str[20];         /*字符串内容*/
} struct_para_str2numb;

/*测试结果*/
typedef struct
{
    float value;                /* 测试值*/
    float max_value;            /* 最大测试值*/
    float min_value;            /* 最小测试值*/
    float aver_value;           /* 平均测试值*/
    int stage_param;            /* 阶梯值*/
    int stage_count[MAX_STAGE]; /* 每个阶段计数*/
} struct_result_data;
typedef struct
{
    int step;
    enum_test_mode mode;                /* 模式0-原命令，1-dd,2-memtester,3-bw,4-net*/
    int pid;                            /* 执行命令的pid*/
    char keyword[ERR_KEYWORD_LEN];      /*关键字*/
    char err_keyword0[ERR_KEYWORD_LEN]; /*错误关键字0*/
    char err_keyword1[ERR_KEYWORD_LEN]; /* 错误关键字1*/
    unsigned long start_time;           /*开始时间*/
    int param_err;                      /*参数错误标志*/
    char dev[MAX_DEVEICE_NUMB];         /*设备名*/
    char dev1[MAX_DEVEICE_NUMB];         /*设备名*/
    char response_flag;                 /*0-发起测试，1-响应测试*/

    int dd_nand;              /* 标志0-emmc 1-nand*/
    int dd_direct;            /*有没有direct*/
    int dd_count;             /*多少次*/
    int real_count;           /*实际执行多少次*/
    float dd_write_threshold; /*写阈值*/
    float dd_read_threshold;  /*读阈值*/
    char dd_path[50];         /*路径*/
    char dd_md5_w[40];        /*写文件MD5值*/
    char dd_md5_cp[40];       /*拷贝文件MD5值*/

    char mem_size[10];
    int mem_numb;

    char net_flag;         /*0-正向打流，1-反向打流*/
    float net_threshold;   /*打流阈值*/
    int net_num;           /*网络号码*/
    unsigned int net_time; /*打流持续时间*/
    int usb3_to_net_flag;  /*usb3.0转网口标志*/

    int ecc_fail;            /*ecc错误计数*/
    int ecc_fial_threshold;  /*ecc错误阈值*/
    int ecc_corr;            /*ecc纠正计数*/
    int ecc_corr_threshold;  /*ecc纠正阈值*/
    int bad_block;           /*坏块计数*/
    int bad_block_threshold; /*坏块阈值*/
    char nand_file_md5[33];  /*nand文件MD5值*/

} struct_cmd_exe_param;
typedef struct
{
    char exe[MAX_CONTENT_LEN];                      /* 测试命令 */
    int state;                                      /* 当前状态*/
    struct_result_data result_data[MAX_VALUE_NUMB]; /* 测试值*/
    int new_data_flag;                              /* 数据有变化为1*/
    int value_numb;                                 /* 几个测试值有效*/
    int err_count;                                  /*错误计数*/
    int pid_err;                                    /*pid错误*/
    // int total_count;                                /*总测试次数计数*/

    unsigned long timeout;          /*超时时间*/
    struct_cmd_exe_param exe_param; /*测试过程参数*/
} struct_test_cmd;

typedef struct
{
    struct_test_cmd cmd[MAX_EXE_ITEM]; /*一条测试项*/
    int numb;                          /* 一组最多10条命令，此处记录实际多少条 */
    int memtester_count;               /*memtester同时测试次数*/
} struct_test_group;

typedef struct
{
    int circle;                         /* 投退,执行多少次*/
    struct_test_group group[MAX_GROUP]; /* 每组里所有命令同时执行，不同组命令先后执行 */

    int groups;     /* 先后执行几组命令，最多执行5组 */
    int cur_groups; /* 当前执行第几组 */

    int sub_log; /* 0-没分项log，有分项log */
    int count;   /* 当前计次*/
    int state;   /* 当前状态*/
    int numb;    /* 当前测试号*/

} struct_test_item;

typedef struct
{
    int pattern;              /*模式，0-发起,顺序执行，1-发起，并行执行，2-回应*/
    int circles;              /*顺序执行模式下，总循环圈数*/
    int interval;             /*执行完一轮测试的时间间隔*/
    unsigned long start_time; /*本轮测试开始时间*/
    int B_can_next_circle;    /*是否可以进行下一轮测试*/
    int interact_flag;        /*是否有交互测试项*/

    char pre_cmd[MAX_PRE_CMD_NUMB][MAX_CONTENT_LEN]; /*预执行命令*/
    int pre_cmd_numb;                                /*预执行命令数量*/
    struct_test_item item[MAX_TEST_ITEM];            /*测试命令管理*/

    int temp_limit;                       /*温限*/
    int temp_limit_count;                 /*超温限计次*/
    char app_path[MAX_CONTENT_LEN];       /*所有日志的路径*/
    char info_path[MAX_CONTENT_LEN];      /*板级信息路径*/
    char info_key[20];                    /*板级信息关键字*/
    char temp_path[MAX_CONTENT_LEN];      /*温度信息路径*/
    char temp_key[20];                    /*温度信息关键字*/
    char limit0_path[MAX_CONTENT_LEN];    /*温限0路径*/
    char limit0_key[20];                  /*温限0关键字*/
    char limit1_path[MAX_CONTENT_LEN];    /*温限1路径*/
    char limit1_key[20];                  /*温限1关键字*/
    char freq_path[MAX_CONTENT_LEN];      /*频率信息路径*/
    char freq_key[20];                    /*频率信息关键字*/
    char list_freq_path[MAX_CONTENT_LEN]; /*频率列表*/
    char list_freq_key[20];               /*频率列表关键字*/
    char hbset[MAX_CONTENT_LEN];          /*设置心跳灯*/
    char hbconctol[MAX_CONTENT_LEN];      /*控制心跳灯*/
    int temp_coef;                        /*温度系数*/
    int check_nand_emmc;                  /*0-EMMC 1-NAND*/
    int net_count;                        /*网络接口数据统计*/

} struct_test;

typedef struct
{
    int run_count;                /*进程运行计次，如果是自启，可以表征断电上电次数*/
    int circle_count;             /*整体测试计次，轮次*/
    char cpu_info[MAX_INFO_LEN];  /*cpu信息*/
    int cpus;                     /*cpu个数*/
    int limit0;                   /*降频温度*/
    int limit1;                   /*重启温度*/
    int list_freq[MAX_FREQ_NUMB]; /*频率列表*/
    int temperature;              /*温度信息*/
    int freq;                     /*频率信息*/
    float cpu_employ;             /*cpu占用率*/
    float mem_employ;             /*内存占用率*/
    float tps;                    /*tps*/
    float kB_read;                /*读速率*/
    float kB_wrtn;                /*写速率*/
    char dev[MAX_DEVEICE_NUMB];   /*EMMC盘*/
} struct_test_info;

typedef struct
{
    unsigned int user;
    unsigned int nice;
    unsigned int sys;
    unsigned int idle;
    unsigned int iowait;
    unsigned int irq;
    unsigned int softirq;
    unsigned int steal;
    unsigned int guest;
    unsigned int guest_nice;
} struct_cpu_employ;

extern struct_test *test;
extern struct_test_info info;
extern struct_cpu_employ old_cpu_employ;
extern pthread_t concurrent_test_thread[MAX_TEST_ITEM];
extern int concurrent_test_signal_fd[MAX_TEST_ITEM][2];
extern pthread_t order_test_thread, response_thread;
extern int order_test_signal_fd[2];
extern int response_signal_fd[2];
extern int led_flag;
extern char log_path[MAX_PATH_LEN];
extern char temp_path[MAX_PATH_LEN];
extern char item_path[MAX_PATH_LEN];
extern char tool_path[MAX_PATH_LEN];
extern pthread_mutex_t mutex_lock;

int get_start_count(char *path, int *count);
int get_cpu_info(char *path, char *cpu_info);
int get_temperature_info(char *path, int *temp, int coef);
int get_freq_info(char *path, int *freq);
int get_cpu_employ(char *path, float *cpu);
void get_cpus(int *cpus);
void get_list_freq(char *list_path, int *list_freq);
void get_temp_limit(char *path_limit0_path, char *path_limit1_path, int *limit0, int *limit1, int coef);
void get_order_test_status(int *status, int *current, struct_test *manag, struct_test_info *infos);
void get_test_name(int current, char *name);
void get_concurrent_test_status(struct_test *all, int *status);
void *response_test_manage(void *arg);
void *concurrent_test_manage(void *arg);
void *in_order_test_manage(void *arg);
void set_heartbeat(char *path);
void conctol_heartbeat(char *path, int value);
int func_get_devname(char *dev, char *path);
void exe_pre_cmd(struct_test *manag);
void func_del_conf(struct_test *manag);
void get_mem_employ(char *path, float *mem, float *mem_free);
void get_tps_r_w(char *dev, float *tps, float *kB_read, float *kB_wrtn);
void Func_test_report(FILE *pFile, struct_test *manag, struct_test_info *infos);
int func_self_check(struct_test *manag);

#endif