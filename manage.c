#include <errno.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <stdarg.h>
#include <ctype.h>
#include <sys/epoll.h>
#include <poll.h>
#include <sys/sysinfo.h>
#include <pthread.h>
#include <termios.h> /*PPSIX 终端控制定义*/
#include <signal.h>
#include <sys/wait.h>
#include <sys/statvfs.h>
#include <dirent.h>
// #include <libudev.h>
#include <time.h>
#include <linux/rtc.h>
#include <sys/ioctl.h>
#include <sys/vfs.h>     /* statfs */
#include <linux/magic.h> /* EXT4_SUPER_MAGIC */
#include <sys/statfs.h>
#include <sys/stat.h>
#include <sys/types.h> // 添加这行
#include <errno.h>     // For errno
#include "config_file.h"
#include "manage.h"
#include "file.h"
#include "display.h"
#include "net_manage.h"

/*
 * @description    : 获取时间ms
 * @param -        : 无
 * @return		   : 当前时间总毫秒数
 */
/*unsigned long func_get_system_time_ms(void)
{
    struct timeval Time;

    gettimeofday(&Time, NULL);
    localtime(&Time.tv_sec);
    return (Time.tv_sec * 1000 + Time.tv_usec / 1000);
}*/
unsigned long func_get_system_time_ms(void)
{
    struct timespec time = {0, 0};

    clock_gettime(CLOCK_MONOTONIC, &time);
    return (time.tv_sec * 1000 + time.tv_nsec / 1000000);
}
/*
 * @description : 获取文件的MD5值
 * @param - *filename : 文件名
 * @return		: MD5值字符串
 */
char *bsp_get_md5_value(char *filename)
{
    FILE *fp;
    static char md5_str[33] = {0}; // MD5值为32个字符+结束符
    char cmd[300] = {0};
    char buf[100] = {0};

    // 构建md5sum命令
    sprintf(cmd, "md5sum %s", filename);

    // 执行命令并获取输出
    fp = popen(cmd, "r");
    if (fp == NULL)
    {
        fprintf(stderr, "Failed to run md5sum command\n");
        return "";
    }

    // 读取命令输出
    if (fgets(buf, sizeof(buf) - 1, fp) != NULL)
    {
        // 检查输出是否包含文件名，确认命令成功执行
        if (strstr(buf, filename) != NULL)
        {
            // md5sum输出格式为: "md5值 文件名"，所以我们只需要前32个字符
            strncpy(md5_str, buf, 32);
            md5_str[32] = '\0'; // 确保字符串正确终止
        }
        else
        {
            // 命令可能失败或输出格式不符合预期
            md5_str[0] = '\0';
        }
    }
    else
    {
        // 没有输出，可能命令失败
        md5_str[0] = '\0';
    }

    pclose(fp);
    return md5_str;
}
/*
 * @description    : 获取设备名，确认是EMMC还是NAND
 * @param - *dev   : 设备名
 * @param - *path  : 挂载路径
 * @return		   : -1-获取异常 0-正确获取
 */
int func_get_devname(char *dev, char *path)
{
    FILE *fstream = NULL;
    char buf[0xff] = {0};
    char temp_dev[MAX_DEVEICE_NUMB] = {0};
    int len = 0;

    len = strlen(path);
    if (*(path + len - 1) == '/')
        strncpy(temp_dev, path, (len - 1));
    else
        strcpy(temp_dev, path);

    if (NULL == (fstream = popen("mount", "r")))
    {
        fprintf(stderr, "execute command failed: %s", strerror(errno));
        return -1;
    }
    else
    {
        while ((fgets(buf, sizeof(buf) - 1, fstream)) != NULL)
        {
            if ((strstr(buf, temp_dev) != NULL))
            {
                if ((strstr(buf, "yaffs") != NULL) || (strstr(buf, "ubi") != NULL))
                {
                    strcpy(dev, test->item[NAND].group[0].cmd[0].exe_param.dev);
                    test->check_nand_emmc = 1;
                }
                else
                {
                    sscanf(buf, "%[^ ]", dev);
                }
                pclose(fstream);
                return 0;
            }
        }
        pclose(fstream);
    }
    return -1;
}
void split_usb_path(const char *full_path, char **controller_path, char **device_path, int *number)
{
    int i = 0;
    char usb_name[10];
    char *usb_pattern = NULL;
    size_t controller_len = 0;
    // 查找"usb"后跟数字的模式，这通常是控制器和设备的分界点
    for (i = 0; i < 9; i++)
    {
        sprintf(usb_name, "/usb%d/", i);
        usb_pattern = strstr(full_path, usb_name);
        if (usb_pattern == NULL)
            continue;
        else
        {
            *number = i;
            if (usb_pattern)
            {
                // 计算控制器路径的长度
                controller_len = usb_pattern - full_path;

                // 分配并复制控制器路径
                *controller_path = malloc(controller_len + 1);
                if (*controller_path)
                {
                    strncpy(*controller_path, full_path, controller_len);
                    (*controller_path)[controller_len] = '\0';
                }

                // 分配并复制设备路径
                *device_path = strdup(usb_pattern);
            }
            else
            {
                // 如果没有找到分界点，整个路径作为控制器路径
                *controller_path = strdup(full_path);
                *device_path = strdup("");
            }
        }
    }
}
// Function to find network interface names under a USB path
int find_network_interfaces(const char *base_path, int *stage, char *network)
{
    DIR *dir;
    struct dirent *entry;
    struct stat statbuf;
    char path[1024];
    int res = 0;
    int current_stage = *stage;

    // Open the directory
    if ((dir = opendir(base_path)) == NULL)
    {
        return 0;
    }

    // Read directory entries
    while ((entry = readdir(dir)) != NULL)
    {
        // Skip "." and ".." entries
        if (strcmp(entry->d_name, ".") == 0 || strcmp(entry->d_name, "..") == 0)
        {
            continue;
        }

        // Create full path
        snprintf(path, sizeof(path), "%s/%s", base_path, entry->d_name);
        // Get file/directory status without following symlinks
        if (lstat(path, &statbuf) == -1)
        {
            continue;
        }

        // Skip symbolic links to avoid loops and unexpected paths
        if (S_ISLNK(statbuf.st_mode))
        {
            continue;
        }

        // Get file/directory status
        if (stat(path, &statbuf) == -1)
        {
            continue;
        }

        // Check if it's a directory
        if (S_ISDIR(statbuf.st_mode))
        {
            // Check if this is a "net" directory
            if (strcmp(entry->d_name, "net") == 0)
            {
                // Found a net directory, look for network interfaces inside
                DIR *net_dir;
                struct dirent *net_entry;

                if ((net_dir = opendir(path)) != NULL)
                {
                    while ((net_entry = readdir(net_dir)) != NULL)
                    {
                        // Skip "." and ".." entries
                        if (strcmp(net_entry->d_name, ".") == 0 ||
                            strcmp(net_entry->d_name, "..") == 0)
                        {
                            continue;
                        }

                        // Found a network interface
                        printf("USB Path: %s\n", base_path);
                        printf("Network Interface: %s\n\n", net_entry->d_name);
                        strcpy(network, net_entry->d_name);
                        closedir(net_dir);
                        closedir(dir);
                        return 1;
                    }
                    closedir(net_dir);
                }
            }
            else if (*stage < 6)
            { // Limit recursion depth to avoid infinite loops
                // Recursively search subdirectories
                int next_stage = current_stage + 1;
                res = find_network_interfaces(path, &next_stage, network);
                if (res == 1)
                {
                    return 1;
                }
            }
        }
    }

    closedir(dir);
    return 0;
}
// 查找指定控制器路径下的所有USB节点
int find_usb_nodes(const char *controller_path, int number, char *network)
{
    int i = 0;
    char full_path[500];
    int res = 0;
    int stage = 0;

    printf("Searching for USB nodes under controller path '%s':\n", controller_path);

    for (i = 0; i < 9; i++)
    {
        if (i == number)
            continue;
        else
        {
            sprintf(full_path, "%s/usb%d", controller_path, i);
            // 确认full_path是否存在
            if (access(full_path, F_OK) != -1)
            {
                printf("usb%d: %s\n", i, full_path);
                // 查找full_path下的网卡节点名
                res = find_network_interfaces(full_path, &stage, network);
                if (res == 1)
                {
                    return 1;
                }
            }
        }
    }
    return 0;
}
// 判断是否是usb3.0
int check_usb3_speed(char *controller_path, int number)
{
    char full_path[500];
    char speed_value[32];
    FILE *fp;
    int is_usb3 = 0;
    sprintf(full_path, "%s/usb%d/speed", controller_path, number);
    printf("full_path: %s\n", full_path);
    // 打开speed文件
    fp = fopen(full_path, "r");
    if (fp == NULL)
    {
        return -1; // 无法读取speed文件
    }

    // 读取speed值
    if (fgets(speed_value, sizeof(speed_value), fp) != NULL)
    {
        // 移除末尾的换行符
        speed_value[strcspn(speed_value, "\n")] = 0;

        // 检查速度值
        if (strstr(speed_value, "480") != NULL)
            is_usb3 = 0;
        else if (strstr(speed_value, "5000") != NULL ||
                 strstr(speed_value, "10000") != NULL ||
                 strstr(speed_value, "SuperSpeed") != NULL)
        {
            is_usb3 = 1;
        }
        else
        {
            // 如果无法确定，默认为USB 2.0
            is_usb3 = 0;
            printf("[info] : Unknown speed value: %s\n", speed_value);
        }
    }

    fclose(fp);
    return is_usb3;
}
/*
 * @description    : 获取网卡名称
 * @param - *dev   : 设备名
 * @param - *path  : 路径
 * @param - usb3_flag: 0-USB2.0 1-USB3.0
 * @return		   : -1-获取异常 0-正确获取
 */
int func_get_netname(char *dev, char *path, int usb3_flag)
{
    FILE *fstream = NULL;
    char buf[0xff] = {0};
    char temp_cmd[0xff] = {0};
    char parent_path[0xf0] = {0};
    char *last_slash = NULL;

    if ((strstr(path, "usb") != NULL) && (usb3_flag == 1))
    {
        char *controller_path = NULL;
        char *device_path = NULL;
        int number = 0;
        int res = 0;

        // 分割路径
        split_usb_path(path, &controller_path, &device_path, &number);
        if (controller_path)
        {
            printf("input_path : %s\n", path);
            printf("controller_path: %s\n", controller_path);
            printf("device_path: %s\n", device_path);
            printf("number: %d\n", number);
            // 确认路径网络节点是否是usb3.0
            res = check_usb3_speed(controller_path, number);
            if (res != 1)
            {
                free(device_path);
                // 查找控制器下其他usb节点是否有生成网络节点
                res = find_usb_nodes(controller_path, number, dev);
                if (res == 1)
                {
                    free(controller_path);
                    printf("find network:%s\n", dev);
                    return 0;
                }
                else
                {
                    free(controller_path);
                    return -1;
                }
            }
        }
        free(device_path);
        free(controller_path);
    }
    // If we didn't find a network interface, try the parent directory
    strcpy(parent_path, path);
    last_slash = strrchr(parent_path, '/');
    if (last_slash != NULL)
    {
        *last_slash = '\0'; // Truncate at the last slash to get parent directory

        memset(temp_cmd, 0, sizeof(temp_cmd));
        sprintf(temp_cmd, "ls %s", parent_path);

        if (NULL == (fstream = popen(temp_cmd, "r")))
        {
            fprintf(stderr, "execute command failed on parent path: %s", strerror(errno));
            return -1;
        }
        else
        {
            while ((fgets(buf, sizeof(buf) - 1, fstream)) != NULL)
            {
                sscanf(buf, "%[a-zA-Z0-9]", dev);
                if (strlen(dev) > 2)
                {
                    pclose(fstream);
                    return 0;
                }
            }
            pclose(fstream);
        }
    }
    return -1;
}
/**
 * 获取指定挂载路径的剩余空间（MB）
 * @param path 挂载路径
 * @return 剩余空间（MB），失败返回-1
 */
unsigned int get_available_space_mb(const char *path)
{
    struct statvfs stat;
    unsigned long long available_space_mb = 0;
    unsigned int available_space_mb_int = 0;

    // 获取文件系统信息
    if (statvfs(path, &stat) != 0)
    {
        perror("statvfs");
        return -1;
    }

    // 计算可用空间（字节）
    unsigned long long available_bytes = (unsigned long long)stat.f_bavail * stat.f_frsize;
    // 转换为MB
    available_space_mb = (available_bytes >> 20);
    available_space_mb_int = (unsigned int)available_space_mb;

    // 转换为MB并返回
    return available_space_mb_int;
}
/*
 * @description     : 通过测试号获取测试名称
 * @param - current : 测试号
 * @return		    : 字符串指针
 */
void get_test_name(int current, char *name)
{
    int i = 0;

    struct_name_str2numb item[MAX_TEST_ITEM] = {
        {TYPE_NO, "UNKNOWN"},
        {SOC, "SOC"},
        {DDR, "DDR"},
        {EMMC, "EMMC"},
        {NAND, "NAND"},
        {CAN, "CAN"},
        {RS485, "RS485"},
        {RS232, "RS232"},
        {SPI, "SPI"},
        {IIC, "IIC"},
        {NET, "NET"},
        {AUDIO, "AUDIO"},
        {CAMERA, "CAMERA"},
        {SATA, "SATA"},
        {RTC, "RTC"},
        {SD, "SD"},
        {USB, "USB"},
        {TYPE_C, "TYPE_C"},
        {G4, "G4"},
        {WIFI, "WIFI"},
        {DIDO, "DIDO"},
        {PCIE, "PCIE"},
        {BT, "BT"},
        {DISPLAY, "DISPLAY"},
    };

    if (current < MAX_TEST_ITEM)
    {
        for (i = 0; i < MAX_TEST_ITEM; i++)
        {
            if (current == item[i].numbs)
            {
                strcpy(name, item[i].str);
                return;
            }
        }
    }
    strcpy(name, item[0].str);
    return;
}
/*
 * @description      : 通过执行命令获取pid的值
 * @param - *command : 参数
 * @return		     : pid号
 */
int get_command_pid(char *command, int numb)
{
    FILE *fstream = NULL;
    char data[250] = {0};
    char tesult[20] = {0};
    int pid_value = 0;
    char command_new[200] = {0};
    int i = 0;

    if (NULL != strstr(command, " &"))
    {
        memcpy(command_new, command, (strlen(command) - strlen(" &")));
    }
    else if (NULL != strstr(command, "iostat"))
    {
        strcpy(command_new, "iostat");
    }
    else
        strcpy(command_new, command);

    sprintf(data, "ps -ax 2>/dev/null | grep '%s' | awk '{print $1}'", command_new);
    // printf("get pid1 = %s\n", data);
    if (NULL == (fstream = popen(data, "r")))
    {
        fprintf(stderr, "execute command_new failed: %s", strerror(errno));
    }
    while ((fgets(tesult, sizeof(tesult) - 1, fstream)) != NULL)
    {
        pid_value = atoi(tesult);
        i++;
        if (i > numb)
            break;
    }
    pclose(fstream);

    if (0 == pid_value)
    {
        memset(data, 0, sizeof(data));
        sprintf(data, "ps |grep '%s' | awk '{print $1}'", command_new);
        // printf("get pid2 = %s\n", data);
        if (NULL == (fstream = popen(data, "r")))
        {
            fprintf(stderr, "execute command_new failed: %s", strerror(errno));
        }
        while ((fgets(tesult, sizeof(tesult) - 1, fstream)) != NULL)
        {
            pid_value = atoi(tesult);
            i++;
            if (i > numb)
                break;
        }
        pclose(fstream);
    }
    return pid_value;
}
/*
 * @description         : 确认该pid进程是否还存在
 * @param - command_pid : 参数
 * @return		        : 0-进程不存在 1-进程存在
 */
int check_command_pid(int command_pid)
{
    char path[50] = {0};

    sprintf(path, "/proc/%d", command_pid);
    if ((access(path, F_OK)) == 0)
    {
        return 1;
    }
    else
    {
        usleep(200000);
        printf("pid=%s,process end\n", path);
        return 0;
    }
    // 注意：以下代码永远不会执行，已移除
    // printf("check1=%s\n", path);
    // return 0;
}
/*
 * @description : 根据pid杀死进程组
 * @param - pids: 该命令pid
 * @return		: 0 ：执行成功 -1 ；执行失败
 */
void kill_command(int pids)
{
    FILE *fstream = NULL;
    char data[200] = {0};

    Func_Dprintf(data, "kill -9 %d", pids);

    if (NULL == (fstream = popen(data, "w")))
    {
        fprintf(stderr, "execute command failed: %s", strerror(errno));
    }
    else
        pclose(fstream);
}
/*
 * @description : 根据关键字杀死进程
 * @param - *cmd: 关键字
 * @return		: 0 ：执行成功 -1 :执行失败
 */
void kill_command_kw(char *cmd)
{
    FILE *fstream = NULL;
    char data[200] = {0};
    int pids = 0;

    pids = get_command_pid(cmd, 0);
    sprintf(data, "kill -9 %d", pids);

    if (NULL == (fstream = popen(data, "w")))
    {
        fprintf(stderr, "execute command failed: %s", strerror(errno));
    }
    else
        pclose(fstream);
}
/*
 * @description    : 自己的popen
 * @param - command: 要执行的命令
 * @param - type   : 类型 读是0，写是1
 * @param - & pid  : 该命令的pid指针
 * @return		   : 0 ：执行成功 -1 ；执行失败
 */
FILE *popen2(char *command, char type, int *pid)
{
    pid_t child_pid;
    int fd[2];
    pipe(fd);

    if ((child_pid = fork()) == -1)
    {
        perror("fork");
        exit(1);
    }

    /* child process */
    if (child_pid == 0)
    {
        if (type == 'r')
        {
            close(fd[READ]);    // Close the READ end of the pipe since the child's fd is write-only
            dup2(fd[WRITE], 1); // Redirect stdout to pipe
        }
        else
        {
            close(fd[WRITE]);  // Close the WRITE end of the pipe since the child's fd is read-only
            dup2(fd[READ], 0); // Redirect stdin to pipe
        }

        setpgid(child_pid, child_pid); // Needed so negative PIDs can kill children of /bin/sh
        execl("/bin/sh", "/bin/sh", "-c", command, NULL);
        exit(0);
    }
    else
    {
        if (type == 'r')
        {
            close(fd[WRITE]); // Close the WRITE end of the pipe since parent's fd is read-only
        }
        else
        {
            close(fd[READ]); // Close the READ end of the pipe since parent's fd is write-only
        }
    }

    *pid = child_pid;

    if (type == 'r')
    {
        return fdopen(fd[READ], "r");
    }

    return fdopen(fd[WRITE], "w");
}
/*
 * @description    : 自己的pclose
 * @param - * fp   : 管道指针
 * @param - pid    : 该命令的pid
 * @return		   : 0 ：执行成功 -1 ；执行失败
 */
int pclose2(FILE *fp, pid_t pid)
{
    int stat = 0;

    fclose(fp);
    while (waitpid(pid, &stat, 0) == -1)
    {
        if (errno != EINTR)
        {
            stat = -1;
            break;
        }
    }

    return stat;
}
/*
 * @description    : 自己的system
 * @param - command: 要执行的命令
 * @param - & pid  : 该命令的pid指针
 * @return		   : 0 ：执行成功 -1 ；执行失败
 */
/*
void bsp_my_system(char *command, int *pid)
{
    FILE *fp = NULL;
    char temp_cmd[100] = {0};

    fp = popen(command, "w");

    if (fp == NULL)
    {
        printf("%s execution error\n", command);
        return;
    }
    else
    {
        pclose(fp);
        usleep(20000);
        sscanf(command, "%[^ ]", temp_cmd);
        printf("%s\n", temp_cmd);
        *pid = get_command_pid(temp_cmd);
    }
}*/
void bsp_my_system(char *command, int *pid)
{
    FILE *fp = NULL;
    int real_pid = 0;
    char temp_cmd[100] = {0};
    int i = 0, j = 0;

    fp = popen2(command, READ, pid);
    if (fp == NULL)
    {
        printf("%s execution error\n", command);
        return;
    }
    else
    {
        pclose2(fp, *pid);
    }
    usleep(20000);
    i = 0;
    j = 0;
    sscanf(command, "%[^ ]", temp_cmd);
    while (*pid > real_pid)
    {
        real_pid = get_command_pid(temp_cmd, j);
        if (0 == real_pid)
            break;
        j++;
        if ((real_pid - *pid) >= 5)
        {
            real_pid = 0;
            j = 0;
        }
        i++;
        if (i > MAX_EXE_ITEM)
            break;
    }
    *pid = real_pid;
    printf("cmd=%s,pid=%d\n", command, real_pid);
}
/*
 * @description    : 自己的system2
 * @param - command: 要执行的命令
 * @param - & pid  : 该命令的pid指针
 * @return		   : 0 ：执行成功 -1 ；执行失败
 */
void bsp_my_system2(char *command, char c)
{
    FILE *fp = NULL;

    if (c == 'w')
        fp = popen(command, "w");
    else
        fp = popen(command, "r");

    if (fp == NULL)
    {
        printf("%s execution error\n", command);
        return;
    }
    else
    {
        pclose(fp);
    }
}

/*
 * @description    : 将命令里的参数解析到测试结构体
 * @param - *cmd   : 命令
 * @param - numb   : 条目枚举号
 * @param - *mem_count: 内存测试次数
 * @return		   : 无
 */
void func_cmd_analyze(struct_test_cmd *cmd, int numb, int *mem_count)
{
    char cmd_separate[7][200] = {0};
    int i = 0, int_value;
    float float_value;
    char name[10] = {0};
    int j = 0, k = 0;

    sscanf(cmd->exe, "%s %s %s %s %s %s %s", cmd_separate[0], cmd_separate[1], cmd_separate[2], cmd_separate[3], cmd_separate[4], cmd_separate[5], cmd_separate[6]);
    if (NULL != strstr(cmd_separate[0], "dd"))
    {
        cmd->exe_param.mode = DD_MODE;
        strcpy(cmd->exe_param.keyword, "copied");
        strcpy(cmd->exe_param.err_keyword0, "failed");
        if (NULL != strstr(cmd_separate[1], "/dev"))
        {
            cmd->exe_param.dd_nand = 1;
            cmd->exe_param.dd_direct = 0;
            strcpy(cmd->exe_param.dev, cmd_separate[1]);
            i = 2;
        }
        else
        {
            i = 1;
            cmd->exe_param.dd_nand = 0;
        }
        if (NULL != strstr(cmd_separate[i], "/"))
        {
            // if (strlen(cmd_separate[i]) <= 2)
            // strcpy(cmd->exe_param.dd_path, "/ ");
            // else
            strcpy(cmd->exe_param.dd_path, cmd_separate[i]);
        }
        else
        {
            cmd->exe_param.param_err = 1;
            return;
        }
        i++;
        int_value = atoi(cmd_separate[i]);
        if (int_value > 0)
            cmd->exe_param.dd_count = int_value;
        else
        {
            cmd->exe_param.param_err = 1;
            return;
        }
        i++;
        float_value = atof(cmd_separate[i]);
        if (float_value > 0.0)
            cmd->exe_param.dd_write_threshold = float_value;
        else
        {
            cmd->exe_param.param_err = 1;
            return;
        }
        i++;
        float_value = atof(cmd_separate[i]);
        if (float_value > 0.0)
            cmd->exe_param.dd_read_threshold = float_value;
        else
        {
            cmd->exe_param.param_err = 1;
            return;
        }
        i++;
        if (NULL != strstr(cmd_separate[i], "direct"))
        {
            cmd->exe_param.dd_direct = 1;
        }
        memset(name, 0, sizeof(name));
        get_test_name(numb, name);
        printf("[info] : %s_dd_%s,write threshold = %.02f,read threshold = %.02f\n", name, cmd->exe_param.dd_path, cmd->exe_param.dd_write_threshold, cmd->exe_param.dd_read_threshold);
    }
    else if (NULL != strstr(cmd_separate[0], "memtester"))
    {
        cmd->exe_param.mode = MEMTER_MODE;
        strcpy(cmd->exe_param.keyword, "Done.");
        strcpy(cmd->exe_param.err_keyword0, "FAIL");
        strcpy(cmd->exe_param.err_keyword1, "fail");
        strcpy(cmd->exe_param.mem_size, cmd_separate[1]);
        int_value = atoi(cmd_separate[2]);
        if (int_value > 0)
            cmd->exe_param.mem_numb = int_value;
        else
        {
            cmd->exe_param.param_err = 1;
            return;
        }
        if (0 == *mem_count)
            *mem_count = 1;
        else
            *mem_count += 1;
    }
    else if (NULL != strstr(cmd_separate[0], "bw_mem"))
    {
        cmd->exe_param.mode = MEM_BW_MODE;
    }
    else if (NULL != strstr(cmd_separate[0], "iperf"))
    {
        cmd->exe_param.mode = NET_MODE;
        strcpy(cmd->exe_param.keyword, "sender");
        strcpy(cmd->exe_param.err_keyword0, "error");
        strcpy(cmd->exe_param.err_keyword1, "unable to connect");
        if (0 != cmd_separate[2][0])
        {
            if (strstr(cmd_separate[2], "usb3") != NULL)
            {
                cmd->exe_param.response_flag = 1;
                cmd->exe_param.usb3_to_net_flag = 1;
            }
            else
            {
                cmd->exe_param.net_flag = atoi(cmd_separate[2]);
                float_value = atof(cmd_separate[3]);
                if (float_value > 0.0)
                    cmd->exe_param.net_threshold = float_value;
                else
                {
                    cmd->exe_param.param_err = 1;
                    return;
                }
            }
        }
        else
            cmd->exe_param.response_flag = 1;
        if (0 != cmd_separate[4][0])
        {
            int_value = atoi(cmd_separate[4]);
            if (int_value > 0)
                cmd->exe_param.net_time = int_value;
            else
            {
                cmd->exe_param.net_time = 600;
            }
        }
        else
        {
            cmd->exe_param.net_time = 600;
        }
        if (0 != cmd_separate[5][0])
        {
            if (strstr(cmd_separate[5], "usb3") != NULL)
                cmd->exe_param.usb3_to_net_flag = 1;
            else
                cmd->exe_param.usb3_to_net_flag = 0;
        }
        else if (cmd->exe_param.response_flag != 1)
        {
            cmd->exe_param.usb3_to_net_flag = 0;
        }
        if (0 != cmd_separate[1][0])
        {
            if (strstr(cmd_separate[1], "/sys/") != NULL)
            {
                if (func_get_netname(cmd->exe_param.dev, cmd_separate[1], cmd->exe_param.usb3_to_net_flag) < 0)
                {
                    cmd->exe_param.param_err = 1;
                }
            }
            else
                strcpy(cmd->exe_param.dev, cmd_separate[1]);
        }
        else
        {
            cmd->exe_param.param_err = 1;
            return;
        }
        memset(name, 0, sizeof(name));
        get_test_name(numb, name);
        if (0 == cmd->exe_param.net_flag)
        {
            cmd->exe_param.net_num = test->net_count;
            printf("[info] : %s_iperf3_net%d,forward, threshold = %.02f ,time = %d, real_dev=%s\n", name, cmd->exe_param.net_num, cmd->exe_param.net_threshold, cmd->exe_param.net_time, cmd->exe_param.dev);
            // 第一路优先配置好ip，用于双板交互
            if (0 == test->net_count)
            {
                char my_cmd[200] = {0};
                if (cmd->exe_param.response_flag == 1)
                {
                    sprintf(my_cmd, "ifconfig %s 192.168.%d.232 netmask *************", cmd->exe_param.dev, cmd->exe_param.net_num);
                    printf(L_BLUE "type test is B" NONE "\n");
                    printf("[info] : TCP server dev=%s,ip=192.168.%d.232\n", cmd->exe_param.dev, cmd->exe_param.net_num);
                }
                else
                {
                    sprintf(my_cmd, "ifconfig %s 192.168.%d.231 netmask *************", cmd->exe_param.dev, cmd->exe_param.net_num);
                    printf(L_BLUE "type test is A" NONE "\n");
                    printf("[info] : TCP client dev=%s,ip=192.168.%d.231\n", cmd->exe_param.dev, cmd->exe_param.net_num);
                }
                bsp_my_system2(my_cmd, 'w');
                memset(my_cmd, 0, sizeof(my_cmd));
                sprintf(my_cmd, "ifconfig %s up", cmd->exe_param.dev);
                bsp_my_system2(my_cmd, 'w');
                sleep(2);
            }
            test->net_count += 1;
        }
        else
        {
            for (j = 0; j < test->item[numb].groups; j++)
            {
                for (k = 0; k < test->item[numb].group[j].numb; k++)
                {
                    char temp_separate[2][200] = {0};
                    sscanf(test->item[numb].group[j].cmd[k].exe, "%s %s", temp_separate[0], temp_separate[1]);
                    if (strstr(cmd_separate[1], temp_separate[1]) != NULL)
                    {
                        cmd->exe_param.net_num = test->item[numb].group[j].cmd[k].exe_param.net_num;
                        break;
                    }
                }
            }

            printf("[info] : %s_iperf3_net%d,reverse, threshold = %.02f ,time = %d, real_dev=%s\n", name, cmd->exe_param.net_num, cmd->exe_param.net_threshold, cmd->exe_param.net_time, cmd->exe_param.dev);
        }
    }
    else if (NULL != strstr(cmd_separate[0], "rtc_test"))
    {
        cmd->exe_param.mode = RTC_TEST_MODE;
        strcpy(cmd->exe_param.dev, cmd_separate[1]);
        strcpy(cmd->exe_param.dev1, cmd_separate[2]);
    }
    else if (NULL != strstr(cmd_separate[0], "check_nand"))
    {
        cmd->exe_param.mode = CHECK_NAND_ERR;
        if (NULL != strstr(cmd_separate[1], "/dev"))
        {
            strcpy(cmd->exe_param.dev, cmd_separate[1]);
        }
        else
        {
            cmd->exe_param.param_err = 1;
        }

        int_value = atoi(cmd_separate[2]);
        if (int_value > 0)
            cmd->exe_param.ecc_fial_threshold = int_value;
        else
        {
            cmd->exe_param.param_err = 1;
        }
        int_value = atoi(cmd_separate[3]);
        if (int_value > 0)
            cmd->exe_param.ecc_corr_threshold = int_value;
        else
        {
            cmd->exe_param.param_err = 1;
        }
        int_value = atoi(cmd_separate[4]);
        if (int_value > 0)
            cmd->exe_param.bad_block_threshold = int_value;
        else
        {
            cmd->exe_param.param_err = 1;
        }
    }
    else
    {
        cmd->exe_param.mode = ANY_MODE;
        strcpy(cmd->exe_param.dev, cmd_separate[1]);
        strcpy(cmd->exe_param.keyword, "PASSED");
        strcpy(cmd->exe_param.err_keyword0, "FAILED");
    }
}
/*
 * @description       : 配置参数处理
 * @param - *manag    : 测试结构体
 * @return	          : 无
 */
void func_del_conf(struct_test *manag)
{
    int i = 0, j = 0, k = 0;

    for (i = 0; i < MAX_TEST_ITEM; i++)
    {
        if (manag->item[i].circle != 0)
        {
            for (j = 0; j < manag->item[i].groups; j++)
            {
                for (k = 0; k < manag->item[i].group[j].numb; k++)
                    func_cmd_analyze(&(manag->item[i].group[j].cmd[k]), i, &manag->item[i].group[j].memtester_count);
            }
        }
    }
}
/*
 * @description      : 统一打印测试结果，存储测试数据
 * @param - *result  : PASSED FAILED
 * @param - items    : 测试项目
 * @param - *mesage  : 错误信息
 * @return		     : 无
 */
void bsp_print_save(char *result, char *items, char *mesage)
{
    if (strstr(result, "PASSED") != NULL)
    {
        printf(L_GREEN "%s test result is PASSED" NONE "\n", items);
        file_write_fmt(pLogFile, "%s test result is PASSED;%s\n", items, mesage);
    }
    else if (strstr(result, "FAILED") != NULL)
    {
        led_flag = 1;
        printf(L_RED "%s test result is FAILED; reason : %s" NONE "\n", items, mesage);
        file_write_fmt(pLogFile, "%s test result is FAILED; reason : %s\n", items, mesage);
    }
}
/*
 * @description      : 处理测试数据，统计最大值，最小值，平均值，分段计数
 * @param - *data    : 数据
 * @return		     : 无
 */
void bsp_data_deal(struct_result_data *data)
{
    if (data->value > data->max_value)
        data->max_value = data->value;
    if (data->min_value > 0.1)
    {
        if (data->value < data->min_value)
            data->min_value = data->value;
    }
    else
        data->min_value = data->value;

    if (data->aver_value > 0.1)
        data->aver_value = (data->aver_value + data->value) / 2;
    else
        data->aver_value = data->value;

    if (data->value < (float)data->stage_param)
        data->stage_count[0]++;
    else if (data->value < (float)(data->stage_param * 2.0f))
        data->stage_count[1]++;
    else if (data->value < (float)(data->stage_param * 3.0f))
        data->stage_count[2]++;
    else if (data->value < (float)(data->stage_param * 4.0f))
        data->stage_count[3]++;
    else
        data->stage_count[4]++;
}
void bsp_save_dmesg(int err_numb)
{
    char my_cmd[150] = {0};
    if (!(err_numb))
    {
        sprintf(my_cmd, "dmesg > %sdmesg.log", log_path);
        bsp_my_system2(my_cmd, 'w');
    }
}
void bsp_clean_caches(void)
{
    bsp_my_system2("sync", 'w');
    bsp_my_system2("sync", 'w');
    bsp_my_system2("echo 3 > /proc/sys/vm/drop_caches", 'w');
}
/*
 * @description : dd测试执行模块
 * @param -*cmd : 执行命令
 * @return		: 无
 */
void func_dd_test(struct_test_cmd *cmd)
{
    char temp_item[100] = {0};
    char err_reson[200] = {0};
    char temp_file_name[200] = {0};
    char my_cmd[500] = {0};
    char temp_dev[MAX_DEVEICE_NUMB] = {0};
    char *p = NULL;
    int len = 0, res = 0;
    unsigned long curr_time;
    char line[500] = {0};
    FILE *tempLogFile = NULL;
    FILE *fp = NULL;
    unsigned int space = 0;

    sprintf(temp_item, "dd path %s ", cmd->exe_param.dd_path);
    switch (cmd->exe_param.step)
    {
    case 0:
    {
        if (1 == cmd->exe_param.param_err)
        {
            cmd->state = CURRENT_STATE_OVER;
            strcpy(err_reson, "test param err");
            bsp_print_save("FAILED", temp_item, err_reson);
            cmd->err_count++;
            return;
        }
        if (0 == cmd->exe_param.dev[0])
        {
            if (strlen(cmd->exe_param.dd_path) < 3)
                res = func_get_devname(cmd->exe_param.dev, "/ ");
            else
                res = func_get_devname(cmd->exe_param.dev, cmd->exe_param.dd_path);
        }
        space = get_available_space_mb(cmd->exe_param.dd_path);
        printf("[info] : %s, available space = %d MB\n", cmd->exe_param.dd_path, space);
        if (space * 3 < (cmd->exe_param.dd_count * 2 * 10)) // 判别是否大于磁盘剩余的30%空间
            cmd->exe_param.real_count = (space * 3) / 20;
        else
            cmd->exe_param.real_count = cmd->exe_param.dd_count;
        if (-1 == res)
        {
            cmd->state = CURRENT_STATE_OVER;
            strcpy(err_reson, "can not find device");
            bsp_print_save("FAILED", temp_item, err_reson);
            bsp_save_dmesg(cmd->err_count);
            cmd->err_count++;
            return;
        }
        if (NULL != (p = strstr(cmd->exe_param.dev, "/dev/")))
        {
            // 找到最后一个斜杠的位置
            char *last_slash = strrchr(cmd->exe_param.dev, '/');
            if (last_slash != NULL)
            {
                // 拷贝最后一个斜杠后面的字符串到temp_dev
                strcpy(temp_dev, last_slash + 1);
            }
            else
            {
                // 如果没有找到斜杠，使用原来的方法
                len = strlen("/dev/");
                strcpy(temp_dev, (p + len));
            }
        }
        bsp_clean_caches();
        if (1 == cmd->exe_param.dd_direct)
            sprintf(my_cmd, "%sdd if=/dev/zero of=%stest.bin bs=2M count=%d conv=fsync oflag=direct 2>&1 | tee -a %s%s_dd_temp.log &", tool_path, cmd->exe_param.dd_path, cmd->exe_param.real_count, temp_path, temp_dev);
        else
            sprintf(my_cmd, "%sdd if=/dev/zero of=%stest.bin bs=2M count=%d conv=fsync 2>&1 | tee -a %s%s_dd_temp.log &", tool_path, cmd->exe_param.dd_path, cmd->exe_param.real_count, temp_path, temp_dev);
        bsp_my_system(my_cmd, &(cmd->exe_param.pid));
        // if (0 == check_command_pid(cmd->exe_param.pid))
        //{
        // cmd->exe_param.pid = 0;
        // cmd->state = CURRENT_STATE_OVER;
        // strcpy(err_reson, "can not exe dd write");
        // bsp_print_save("FAILED", temp_item, err_reson);
        // bsp_save_dmesg(cmd->err_count);
        // cmd->err_count++;
        //}
        // else
        // {
        memset(my_cmd, 0, sizeof(my_cmd));
        sprintf(my_cmd, "%siostat %s -x 1 &", tool_path, cmd->exe_param.dev);
        bsp_my_system2(my_cmd, 'w');
        cmd->exe_param.step++;
        cmd->exe_param.start_time = func_get_system_time_ms();
        //}
    }
    break;
    case 1: //
    {
        curr_time = func_get_system_time_ms();
        if ((curr_time - cmd->exe_param.start_time) > (cmd->timeout * 1000))
        {
            kill_command(cmd->exe_param.pid);
            cmd->exe_param.pid = 0;
            cmd->state = CURRENT_STATE_OVER;
            strcpy(err_reson, "dd write test timeout");
            bsp_print_save("FAILED", temp_item, err_reson);
            memset(my_cmd, 0, sizeof(my_cmd));
            sprintf(my_cmd, "%siostat %s", tool_path, cmd->exe_param.dev);
            kill_command_kw(my_cmd);
            bsp_save_dmesg(cmd->err_count);

            cmd->err_count++;
        }
        else
        {
            if (0 != cmd->exe_param.pid)
            {
                if (0 == check_command_pid(cmd->exe_param.pid))
                    cmd->exe_param.pid = 0;
            }
            else
            {
                if (NULL != (p = strstr(cmd->exe_param.dev, "/dev/")))
                {
                    // 找到最后一个斜杠的位置
                    char *last_slash = strrchr(cmd->exe_param.dev, '/');
                    if (last_slash != NULL)
                    {
                        // 拷贝最后一个斜杠后面的字符串到temp_dev
                        strcpy(temp_dev, last_slash + 1);
                    }
                    else
                    {
                        // 如果没有找到斜杠，使用原来的方法
                        len = strlen("/dev/");
                        strcpy(temp_dev, (p + len));
                    }
                }
                // 确认测试结果
                sprintf(temp_file_name, "%s%s_dd_temp.log", temp_path, temp_dev);
                tempLogFile = file_open(temp_file_name, ONLY_R);
                if (NULL == tempLogFile)
                {
                    cmd->state = CURRENT_STATE_OVER;
                    strcpy(err_reson, "dd test temp file err");
                    bsp_print_save("FAILED", temp_item, err_reson);
                    cmd->err_count++;
                    memset(my_cmd, 0, sizeof(my_cmd));
                    sprintf(my_cmd, "%siostat %s", tool_path, cmd->exe_param.dev);
                    kill_command_kw(my_cmd);

                    return;
                }
                fseek(tempLogFile, 0, SEEK_SET);

                while (!feof(tempLogFile))
                {
                    memset(line, 0, sizeof(line));
                    if (fgets(line, sizeof(line) - 1, tempLogFile) != NULL)
                    {
                        if (NULL != (p = strstr(line, cmd->exe_param.keyword)))
                        {
                            char temp_data[12][30];
                            sscanf(line, "%s %s %s %s %s %s %s %s %s %s %s", temp_data[0], temp_data[1], temp_data[2], temp_data[3], temp_data[4], temp_data[5], temp_data[6], temp_data[7], temp_data[8], temp_data[9], temp_data[10]);
                            cmd->result_data[0].value = atof(temp_data[9]);
                            bsp_data_deal(&(cmd->result_data[0]));

                            // 计算写文件MD5
                            memset(line, 0, sizeof(line));
                            memset(my_cmd, 0, sizeof(my_cmd));
                            sprintf(my_cmd, "md5sum %stest.bin", cmd->exe_param.dd_path);
                            // printf("md5sum cmd = %s\n", my_cmd);
                            fp = popen(my_cmd, "r");
                            if (fp != NULL)
                            {
                                while ((fgets(line, sizeof(line) - 1, fp)) != NULL)
                                {
                                    // printf("src md5 = %s\n", line);
                                    if ((strstr(line, "bin") != NULL))
                                    {
                                        sscanf(line, "%[^ ]", cmd->exe_param.dd_md5_w);
                                    }
                                }
                                pclose(fp);
                            }
                            cmd->exe_param.step++;
                            break;
                        }
                        if (NULL != (strstr(line, cmd->exe_param.err_keyword0)))
                        {
                            cmd->state = CURRENT_STATE_OVER;
                            strcpy(err_reson, "dd exe err");
                            bsp_print_save("FAILED", temp_item, err_reson);
                            bsp_save_dmesg(cmd->err_count);
                            cmd->err_count++;
                            memset(my_cmd, 0, sizeof(my_cmd));
                            sprintf(my_cmd, "%siostat %s", tool_path, cmd->exe_param.dev);
                            kill_command_kw(my_cmd);
                        }
                    }
                }
                fseek(tempLogFile, 0, SEEK_SET);
                file_add(pLogFile, tempLogFile);
                file_close(tempLogFile);
                unlink(temp_file_name);
            }
        }
    }
    break;
    case 2: //
    {
        if (NULL != (p = strstr(cmd->exe_param.dev, "/dev/")))
        {
            // 找到最后一个斜杠的位置
            char *last_slash = strrchr(cmd->exe_param.dev, '/');
            if (last_slash != NULL)
            {
                // 拷贝最后一个斜杠后面的字符串到temp_dev
                strcpy(temp_dev, last_slash + 1);
            }
            else
            {
                // 如果没有找到斜杠，使用原来的方法
                len = strlen("/dev/");
                strcpy(temp_dev, (p + len));
            }
        }
        bsp_clean_caches();
        if (1 == cmd->exe_param.dd_direct)
            sprintf(my_cmd, "%sdd if=%stest.bin of=/dev/null count=%d bs=2M iflag=direct 2>&1 | tee -a %s%s_dd_temp.log &", tool_path, cmd->exe_param.dd_path, cmd->exe_param.real_count, temp_path, temp_dev);
        else
            sprintf(my_cmd, "%sdd if=%stest.bin of=/dev/null count=%d bs=2M 2>&1 | tee -a %s%s_dd_temp.log &", tool_path, cmd->exe_param.dd_path, cmd->exe_param.real_count, temp_path, temp_dev);
        bsp_my_system(my_cmd, &(cmd->exe_param.pid));
        // if (0 == check_command_pid(cmd->exe_param.pid))
        // {
        // cmd->exe_param.pid = 0;
        // cmd->state = CURRENT_STATE_OVER;
        // strcpy(err_reson, "can not exe dd read");
        // bsp_print_save("FAILED", temp_item, err_reson);
        // bsp_save_dmesg(cmd->err_count);
        // cmd->err_count++;
        // memset(my_cmd, 0, sizeof(my_cmd));
        // sprintf(my_cmd, "%siostat %s", tool_path, cmd->exe_param.dev);
        // kill_command_kw(my_cmd);
        //}
        // else
        //{
        memset(my_cmd, 0, sizeof(my_cmd));
        cmd->exe_param.step++;
        cmd->exe_param.start_time = func_get_system_time_ms();
        // }
    }
    break;
    case 3: //
    {
        curr_time = func_get_system_time_ms();
        if ((curr_time - cmd->exe_param.start_time) > (cmd->timeout * 1000))
        {
            kill_command(cmd->exe_param.pid);
            cmd->exe_param.pid = 0;
            cmd->state = CURRENT_STATE_OVER;
            strcpy(err_reson, "dd read test timeout");
            bsp_print_save("FAILED", temp_item, err_reson);
            bsp_save_dmesg(cmd->err_count);
            cmd->err_count++;
            memset(my_cmd, 0, sizeof(my_cmd));
            sprintf(my_cmd, "%siostat %s", tool_path, cmd->exe_param.dev);
            kill_command_kw(my_cmd);
        }
        else
        {
            if (0 != cmd->exe_param.pid)
            {
                if (0 == check_command_pid(cmd->exe_param.pid))
                    cmd->exe_param.pid = 0;
            }
            else
            {
                memset(my_cmd, 0, sizeof(my_cmd));
                sprintf(my_cmd, "%siostat %s", tool_path, cmd->exe_param.dev);
                kill_command_kw(my_cmd);

                // 确认测试结果
                if (NULL != (p = strstr(cmd->exe_param.dev, "/dev/")))
                {
                    // 找到最后一个斜杠的位置
                    char *last_slash = strrchr(cmd->exe_param.dev, '/');
                    if (last_slash != NULL)
                    {
                        // 拷贝最后一个斜杠后面的字符串到temp_dev
                        strcpy(temp_dev, last_slash + 1);
                    }
                    else
                    {
                        // 如果没有找到斜杠，使用原来的方法
                        len = strlen("/dev/");
                        strcpy(temp_dev, (p + len));
                    }
                }
                sprintf(temp_file_name, "%s%s_dd_temp.log", temp_path, temp_dev);
                tempLogFile = file_open(temp_file_name, ONLY_R);
                if (NULL == tempLogFile)
                {
                    cmd->state = CURRENT_STATE_OVER;
                    strcpy(err_reson, "dd test temp file err");
                    bsp_print_save("FAILED", temp_item, err_reson);
                    cmd->err_count++;
                    return;
                }

                fseek(tempLogFile, 0, SEEK_SET);

                while (!feof(tempLogFile))
                {
                    memset(line, 0, sizeof(line));
                    if (fgets(line, sizeof(line) - 1, tempLogFile) != NULL)
                    {
                        if (NULL != (p = strstr(line, cmd->exe_param.keyword)))
                        {
                            char temp_data[12][30];
                            sscanf(line, "%s %s %s %s %s %s %s %s %s %s %s", temp_data[0], temp_data[1], temp_data[2], temp_data[3], temp_data[4], temp_data[5], temp_data[6], temp_data[7], temp_data[8], temp_data[9], temp_data[10]);
                            cmd->result_data[1].value = atof(temp_data[9]);
                            bsp_data_deal(&(cmd->result_data[1]));
                            cmd->new_data_flag = 1;
                            cmd->exe_param.step++;
                            break;
                        }
                        if (NULL != (strstr(line, cmd->exe_param.err_keyword0)))
                        {
                            cmd->state = CURRENT_STATE_OVER;
                            strcpy(err_reson, "dd read exe err");
                            bsp_print_save("FAILED", temp_item, err_reson);
                            bsp_save_dmesg(cmd->err_count);
                            cmd->err_count++;
                        }
                    }
                }
                fseek(tempLogFile, 0, SEEK_SET);
                file_add(pLogFile, tempLogFile);
                file_close(tempLogFile);
                unlink(temp_file_name);
            }
        }
    }
    break;
    case 4: //
    {
        bsp_clean_caches();
        if (1 == cmd->exe_param.dd_direct)
            sprintf(my_cmd, "%sdd if=%stest.bin of=%stest_cp.bin bs=2M count=%d conv=fsync oflag=direct &", tool_path, cmd->exe_param.dd_path, cmd->exe_param.dd_path, cmd->exe_param.real_count);
        else
            sprintf(my_cmd, "%sdd if=%stest.bin of=%stest_cp.bin bs=2M count=%d conv=fsync &", tool_path, cmd->exe_param.dd_path, cmd->exe_param.dd_path, cmd->exe_param.real_count);

        bsp_my_system(my_cmd, &(cmd->exe_param.pid));
        // if (0 == check_command_pid(cmd->exe_param.pid))
        //{
        // cmd->exe_param.pid = 0;
        // cmd->state = CURRENT_STATE_OVER;
        // strcpy(err_reson, "can not exe dd cp");
        // bsp_print_save("FAILED", temp_item, err_reson);
        // bsp_save_dmesg(cmd->err_count);
        // cmd->err_count++;
        //}
        // else
        //{
        memset(my_cmd, 0, sizeof(my_cmd));
        cmd->exe_param.step++;
        cmd->exe_param.start_time = func_get_system_time_ms();
        //}
    }
    break;
    case 5: //
    {
        curr_time = func_get_system_time_ms();
        if ((curr_time - cmd->exe_param.start_time) > (cmd->timeout * 1000))
        {
            kill_command(cmd->exe_param.pid);
            cmd->exe_param.pid = 0;
            cmd->state = CURRENT_STATE_OVER;
            strcpy(err_reson, "dd cp test timeout");
            bsp_print_save("FAILED", temp_item, err_reson);
            bsp_save_dmesg(cmd->err_count);
            cmd->err_count++;
        }
        else
        {
            if (0 != cmd->exe_param.pid)
            {
                if (0 == check_command_pid(cmd->exe_param.pid))
                    cmd->exe_param.pid = 0;
            }
            else
            {
                // 计算写文件MD5
                memset(line, 0, sizeof(line));
                memset(my_cmd, 0, sizeof(my_cmd));
                sprintf(my_cmd, "md5sum %stest_cp.bin", cmd->exe_param.dd_path);
                // printf("md5sum cmd = %s\n", my_cmd);
                fp = popen(my_cmd, "r");
                if (fp != NULL)
                {
                    while ((fgets(line, sizeof(line) - 1, fp)) != NULL)
                    {
                        // printf("cp md5 = %s\n", line);
                        if ((strstr(line, "bin") != NULL))
                        {
                            sscanf(line, "%[^ ]", cmd->exe_param.dd_md5_cp);
                        }
                    }
                    pclose(fp);
                }
                // 删除文件
                memset(temp_file_name, 0, sizeof(temp_file_name));
                sprintf(temp_file_name, "%stest_cp.bin", cmd->exe_param.dd_path);
                unlink(temp_file_name);
                memset(temp_file_name, 0, sizeof(temp_file_name));
                sprintf(temp_file_name, "%stest.bin", cmd->exe_param.dd_path);
                unlink(temp_file_name);

                // 测试结果判别
                if ((cmd->result_data[0].value < cmd->exe_param.dd_write_threshold) || (cmd->result_data[1].value < cmd->exe_param.dd_read_threshold))
                {
                    // printf("dd_md5_w = %s,dd_md5_cp = %s\n", cmd->exe_param.dd_md5_w, cmd->exe_param.dd_md5_cp);
                    if (0 != strncmp(cmd->exe_param.dd_md5_w, cmd->exe_param.dd_md5_cp, 32))
                    {
                        sprintf(err_reson, "write_thre:%.2f,write_val:%.2f,read_thre:%.2f,read_val:%.2f,md5sum err", cmd->exe_param.dd_write_threshold, cmd->result_data[0].value, cmd->exe_param.dd_read_threshold, cmd->result_data[1].value);
                    }
                    else
                        sprintf(err_reson, "write_thre:%.2f,write_val:%.2f,read_thre:%.2f,read_val:%.2f,md5sum ok", cmd->exe_param.dd_write_threshold, cmd->result_data[0].value, cmd->exe_param.dd_read_threshold, cmd->result_data[1].value);
                    cmd->state = CURRENT_STATE_OVER;
                    bsp_print_save("FAILED", temp_item, err_reson);
                    cmd->err_count++;
                }
                else
                {
                    if (0 != strncmp(cmd->exe_param.dd_md5_w, cmd->exe_param.dd_md5_cp, 32))
                    {
                        sprintf(err_reson, "write_thre:%.2f,write_val:%.2f,read_thre:%.2f,read_val:%.2f,md5sum err", cmd->exe_param.dd_write_threshold, cmd->result_data[0].value, cmd->exe_param.dd_read_threshold, cmd->result_data[1].value);
                        bsp_print_save("FAILED", temp_item, err_reson);
                        cmd->err_count++;
                    }
                    else
                    {
                        sprintf(err_reson, "write_thre:%.2f,write_val:%.2f,read_thre:%.2f,read_val:%.2f,md5sum ok", cmd->exe_param.dd_write_threshold, cmd->result_data[0].value, cmd->exe_param.dd_read_threshold, cmd->result_data[1].value);
                        bsp_print_save("PASSED", temp_item, err_reson);
                    }
                    cmd->state = CURRENT_STATE_OVER;
                }
            }
        }
    }
    break;
    default:
    {
        cmd->state = CURRENT_STATE_OVER;
        strcpy(err_reson, "dd exe err");
        bsp_print_save("FAILED", temp_item, err_reson);
        cmd->err_count++;
        return;
    }
    }
}
/*
 * @description         : 祛除退格
 * @param -  *inputFile : 源文件指针
 * @param -  *outputFile: 新文件指针
 * @return		        : 无
 */
void bsp_remove_backspace(FILE *inputFile, FILE *outputFile)
{
    char currentChar, prevChar;
    prevChar = EOF; // 初始时上一个字符为文件结束符

    // 逐字符读取输入文件
    // while ((currentChar = fgetc(inputFile)) != EOF)
    while (1)
    {
        currentChar = fgetc(inputFile);
        if ((currentChar <= 0) || (currentChar == 0xff))
            return;
        if (currentChar == '\b')
        { // 如果当前字符是退格转义符
            if (prevChar != EOF)
            {
                // 将光标移到前一个位置
                fseek(outputFile, -1, SEEK_CUR);
                prevChar = fgetc(outputFile);

                // 覆盖前一个字符
                fseek(outputFile, -1, SEEK_CUR);
                fputc(' ', outputFile);

                // 将光标移到下一个位置
                fseek(outputFile, -1, SEEK_CUR);
            }
        }
        else
        {
            fputc(currentChar, outputFile);
            prevChar = currentChar;
        }
    }

    return;
}
/*
 * @description : memtester测试执行模块
 * @param -*cmd : 命令
 * @param -numb : 测试编号
 * @param -mem_count : 测试内存数量
 * @return		: 无
 */
void func_memter_test(struct_test_cmd *cmd, int numb, int mem_count)
{
    char temp_item[100] = {0};
    char err_reson[200] = {0};
    char temp_file_name[200] = {0};
    char my_cmd[300] = {0};
    unsigned long curr_time;
    char line[500] = {0};
    FILE *tempLogFile = NULL, *relogLogFile = NULL;
    int have_res = 0;
    float free_mem = 0.0f, set_mem_f = 0.0f, real_mem = 0.0f;
    int set_mem = 0;

    sprintf(temp_item, "memtester %d", numb);
    switch (cmd->exe_param.step)
    {
    case 0:
    {
        if (1 == cmd->exe_param.param_err)
        {
            cmd->state = CURRENT_STATE_OVER;
            strcpy(err_reson, "test param err");
            bsp_print_save("FAILED", temp_item, err_reson);
            cmd->err_count++;
            return;
        }
        else
        {
            cmd->pid_err = 0;
            cmd->exe_param.step++;
        }
    }
    break;
    case 1:
    {
        get_mem_employ("/proc/meminfo", NULL, &free_mem); // 单位是KB
        free_mem = free_mem / 1024.0f;                    // 单位是MB
        free_mem = free_mem * 0.6f;                       // 单位是MB
        free_mem = free_mem / (float)(mem_count * 1.0f);
        set_mem = atoi(cmd->exe_param.mem_size);
        set_mem_f = (float)set_mem;
        if (set_mem_f > free_mem)
        {
            real_mem = free_mem;
            set_mem = (int)real_mem;
            char temp[20] = {0};
            sprintf(temp, "%dM", set_mem);
            sprintf(my_cmd, "%smemtester %s %d 2>&1 | tee -a %smemtester%d_temp.log &", tool_path, temp, cmd->exe_param.mem_numb, temp_path, numb);
        }
        else
            sprintf(my_cmd, "%smemtester %s %d 2>&1 | tee -a %smemtester%d_temp.log &", tool_path, cmd->exe_param.mem_size, cmd->exe_param.mem_numb, temp_path, numb);

        bsp_my_system(my_cmd, &(cmd->exe_param.pid));
        if (0 == check_command_pid(cmd->exe_param.pid))
        {
            if (cmd->pid_err > PID_ERR_MAX)
            {
                cmd->exe_param.pid = 0;
                cmd->state = CURRENT_STATE_OVER;
                strcpy(err_reson, "can not exe memtester");
                bsp_print_save("FAILED", temp_item, err_reson);
                bsp_save_dmesg(cmd->err_count);
                cmd->err_count++;
                bsp_my_system2("ps -ax", 'w');
                bsp_my_system2("ps", 'w');
            }
            else
            {
                cmd->pid_err++;
            }
        }
        else
        {
            cmd->pid_err = 0;
            cmd->exe_param.step++;
            cmd->exe_param.start_time = func_get_system_time_ms();
        }
    }
    break;
    case 2: //
    {
        curr_time = func_get_system_time_ms();
        if ((curr_time - cmd->exe_param.start_time) > (cmd->timeout * 1000))
        {
            kill_command(cmd->exe_param.pid);
            cmd->exe_param.pid = 0;
            cmd->state = CURRENT_STATE_OVER;
            strcpy(err_reson, "memtester test timeout");
            bsp_print_save("FAILED", temp_item, err_reson);
            bsp_save_dmesg(cmd->err_count);
            cmd->err_count++;
        }
        else
        {
            if (0 != cmd->exe_param.pid)
            {
                if (0 == check_command_pid(cmd->exe_param.pid))
                {
                    cmd->exe_param.pid = 0;
                    sleep(1);
                    bsp_clean_caches();
                    sleep(2);
                }
            }
            else
            {
                // 确认测试结果
                sprintf(temp_file_name, "%smemtester%d_temp.log", temp_path, numb);
                tempLogFile = file_open(temp_file_name, ONLY_R);
                if (NULL == tempLogFile)
                {
                    cmd->state = CURRENT_STATE_OVER;
                    strcpy(err_reson, "memtester temp file err");
                    bsp_print_save("FAILED", temp_item, err_reson);
                    bsp_save_dmesg(cmd->err_count);
                    cmd->err_count++;
                    return;
                }

                fseek(tempLogFile, 0, SEEK_SET);

                while (!feof(tempLogFile))
                {
                    memset(line, 0, sizeof(line));
                    if (fgets(line, sizeof(line) - 1, tempLogFile) != NULL)
                    {
                        if ((NULL != (strstr(line, cmd->exe_param.err_keyword0))) || (NULL != (strstr(line, cmd->exe_param.err_keyword1))))
                        {
                            cmd->state = CURRENT_STATE_OVER;
                            strcpy(err_reson, "memtester have err keyword");
                            bsp_print_save("FAILED", temp_item, err_reson);
                            bsp_save_dmesg(cmd->err_count);
                            cmd->err_count++;
                            have_res = 1;
                            break;
                        }
                        else if (NULL != (strstr(line, cmd->exe_param.keyword)))
                        {
                            cmd->state = CURRENT_STATE_OVER;
                            bsp_print_save("PASSED", temp_item, err_reson);
                            have_res = 1;
                            break;
                        }
                    }
                }
                if (!have_res)
                {

                    cmd->state = CURRENT_STATE_OVER;
                    strcpy(err_reson, "memtester have not pass keyword");
                    bsp_print_save("FAILED", temp_item, err_reson);
                    bsp_save_dmesg(cmd->err_count);
                    cmd->err_count++;
                    printf("!!!!!!!!!!!!! %s file err !!!!!!!!!\n", temp_file_name);
                    memset(my_cmd, 0, sizeof(my_cmd));
                    sprintf(my_cmd, "cat %s", temp_file_name);
                    bsp_my_system2(my_cmd, 'w');
                }

                // 清除多余信息
                memset(temp_file_name, 0, sizeof(temp_file_name));
                sprintf(temp_file_name, "%smemtester%d_reorg.log", temp_path, numb);
                relogLogFile = file_open(temp_file_name, WTWR);
                fseek(tempLogFile, 0, SEEK_SET);
                bsp_remove_backspace(tempLogFile, relogLogFile);
                fflush(relogLogFile); /*将数据同步至ROM*/
                file_close(tempLogFile);
                fseek(relogLogFile, 0, SEEK_SET);
                file_add(pLogFile, relogLogFile);
                file_close(relogLogFile);
                unlink(temp_file_name);
                memset(temp_file_name, 0, sizeof(temp_file_name));
                sprintf(temp_file_name, "%smemtester%d_temp.log", temp_path, numb);
                unlink(temp_file_name);
            }
        }
    }
    break;
    default:
    {
        cmd->state = CURRENT_STATE_OVER;
        strcpy(err_reson, "memtester exe err");
        bsp_print_save("FAILED", temp_item, err_reson);
        cmd->err_count++;
        return;
    }
    }
}
/*
 * @description : 内存带宽测试执行模块
 * @param -*cmd : 命令
 * @return		: 无
 */
void func_mem_bw_test(struct_test_cmd *cmd)
{
    char temp_item[100] = {0};
    char my_cmd[300] = {0};
    char para[9][10] = {"rd", "wr", "rdwr", "cp", "frd", "fwr", "fcp", "bzero", "bcopy"};
    int temp_it = 0, temp_cir = 0;
    char temp_file_name[200] = {0};
    char err_reson[100] = {0};
    FILE *tempLogFile = NULL;

    sprintf(temp_item, "mem bw");

    if (0 == cmd->exe_param.step)
    {
        if (1 == cmd->exe_param.param_err)
        {
            cmd->state = CURRENT_STATE_OVER;
            strcpy(err_reson, "test param err");
            bsp_print_save("FAILED", temp_item, err_reson);
            cmd->err_count++;
            return;
        }
    }
    if (cmd->exe_param.step < 135)
    {
        temp_it = cmd->exe_param.step / 15;
        temp_cir = cmd->exe_param.step % 15;
        if (temp_cir < 5)
        {
            if (0 == temp_cir)
            {
                memset(my_cmd, 0, sizeof(my_cmd));
                sprintf(my_cmd, "echo \"L1 cache bandwidth %s test\" | tee -a %smem_bw.log", para[temp_it], temp_path);
                bsp_my_system2(my_cmd, 'w');
            }
            memset(my_cmd, 0, sizeof(my_cmd));
            sprintf(my_cmd, "%sbw_mem -P 1 8k %s 2>&1 | tee -a %smem_bw.log", tool_path, para[temp_it], temp_path);
            bsp_my_system2(my_cmd, 'w');
            cmd->exe_param.step++;
            return;
        }
        else if (temp_cir < 10)
        {
            if (5 == temp_cir)
            {
                memset(my_cmd, 0, sizeof(my_cmd));
                sprintf(my_cmd, "echo \"L2 cache bandwidth %s test\" | tee -a %smem_bw.log", para[temp_it], temp_path);
                bsp_my_system2(my_cmd, 'w');
            }
            memset(my_cmd, 0, sizeof(my_cmd));
            sprintf(my_cmd, "%sbw_mem -P 1 128k %s 2>&1 | tee -a %smem_bw.log", tool_path, para[temp_it], temp_path);
            bsp_my_system2(my_cmd, 'w');
            cmd->exe_param.step++;
            return;
        }
        else
        {
            if (10 == temp_cir)
            {
                memset(my_cmd, 0, sizeof(my_cmd));
                sprintf(my_cmd, "echo \"Main cache bandwidth %s test\" | tee -a %smem_bw.log", para[temp_it], temp_path);
                bsp_my_system2(my_cmd, 'w');
            }
            memset(my_cmd, 0, sizeof(my_cmd));
            sprintf(my_cmd, "%sbw_mem -P 1 50m %s 2>&1 | tee -a %smem_bw.log", tool_path, para[temp_it], temp_path);
            bsp_my_system(my_cmd, &(cmd->exe_param.pid));
            while (check_command_pid(cmd->exe_param.pid))
            {
                usleep(1000);
            }
            cmd->exe_param.pid = 0;
            // bsp_my_system2(my_cmd, 'w');
            cmd->exe_param.step++;
            return;
        }
    }
    else
    {
        // 确认测试结果
        sprintf(temp_file_name, "%smem_bw.log", temp_path);
        tempLogFile = file_open(temp_file_name, ONLY_R);
        if (NULL == tempLogFile)
        {
            cmd->state = CURRENT_STATE_OVER;
            strcpy(err_reson, "memtester temp file err");
            bsp_print_save("FAILED", temp_item, err_reson);
            cmd->err_count++;
            return;
        }
        fseek(tempLogFile, 0, SEEK_SET);
        file_add(pLogFile, tempLogFile);
        file_close(tempLogFile);
        unlink(temp_file_name);
        cmd->state = CURRENT_STATE_OVER;
        bsp_print_save("PASSED", temp_item, err_reson);
    }
}

/*
 * @description : 网络测试执行模块
 * @param -*cmd : 命令
 * @return		: 无
 */
void func_net_test(struct_test_cmd *cmd)
{
    char temp_item[100] = {0};
    char err_reson[200] = {0};
    char temp_file_name[200] = {0};
    char my_cmd[400] = {0};
    unsigned long curr_time;
    char line[500] = {0};
    FILE *tempLogFile = NULL;
    char *p = NULL;
    int have_res = 0;

    sprintf(temp_item, "iperf3 %s", cmd->exe_param.dev);
    switch (cmd->exe_param.step)
    {
    case 0:
    {
        if (1 == cmd->exe_param.param_err)
        {
            cmd->state = CURRENT_STATE_OVER;
            strcpy(err_reson, "iperf3 param err");
            bsp_print_save("FAILED", temp_item, err_reson);
            cmd->err_count++;
            return;
        }
        else
        {
            cmd->pid_err = 0;
            cmd->exe_param.step++;
        }
    }
    break;
    case 1:
    {
        /*
        int numb = 0;
        int get_numb_1 = 0xff, get_numb_2 = 0xff; // 因为1046的网卡名为fm1-mc0~6
        sscanf(cmd->exe_param.dev, "%*[^0-9]%d%*[^0-9]%d", &get_numb_1, &get_numb_2);
        if (0xff != get_numb_2)
            numb = get_numb_2;
        else
            numb = get_numb_1;*/
        char cmd_separate[2][200] = {0};
        sscanf(cmd->exe, "%s %s", cmd_separate[0], cmd_separate[1]);
        if (strstr(cmd_separate[1], "/sys/") != NULL)
        {
            printf("path=%s,dev=%s,vir_net=net%d\n", cmd_separate[1], cmd->exe_param.dev, cmd->exe_param.net_num);
            if (func_get_netname(cmd->exe_param.dev, cmd_separate[1], cmd->exe_param.usb3_to_net_flag) < 0)
            {
                cmd->exe_param.param_err = 1;
            }
        }
        else
            printf("dev=%s,vir_net=net%d\n", cmd->exe_param.dev, cmd->exe_param.net_num);

        if (1 == cmd->exe_param.param_err)
        {
            cmd->state = CURRENT_STATE_OVER;
            strcpy(err_reson, "iperf3 param err");
            bsp_print_save("FAILED", temp_item, err_reson);
            cmd->err_count++;
            return;
        }

        if (1 == cmd->exe_param.response_flag)
        {
            memset(my_cmd, 0, sizeof(my_cmd));
            sprintf(my_cmd, "ifconfig %s 192.168.%d.232 netmask *************", cmd->exe_param.dev, cmd->exe_param.net_num);
            bsp_my_system2(my_cmd, 'w');
            memset(my_cmd, 0, sizeof(my_cmd));
            sprintf(my_cmd, "ifconfig %s up", cmd->exe_param.dev);
            bsp_my_system2(my_cmd, 'w');
            sleep(2);
            memset(my_cmd, 0, sizeof(my_cmd));
            sprintf(my_cmd, "%siperf3 -s -B 192.168.%d.232 2>&1 | tee -a %snet%d_history.log &", tool_path, cmd->exe_param.net_num, log_path, cmd->exe_param.net_num);
            bsp_my_system(my_cmd, &(cmd->exe_param.pid));
            if (0 == check_command_pid(cmd->exe_param.pid))
            {
                cmd->exe_param.pid = 0;
                cmd->state = CURRENT_STATE_OVER;
                strcpy(err_reson, "can not exe iperf3");
                bsp_print_save("FAILED", temp_item, err_reson);
            }
            else
            {
                cmd->exe_param.step++;
            }
        }
        else
        {
            memset(my_cmd, 0, sizeof(my_cmd));
            sprintf(my_cmd, "ifconfig %s 192.168.%d.231 netmask *************", cmd->exe_param.dev, cmd->exe_param.net_num);
            bsp_my_system2(my_cmd, 'w');
            sleep(2);
            memset(my_cmd, 0, sizeof(my_cmd));
            if (0 == cmd->exe_param.net_flag)
            {
                sprintf(my_cmd, "%siperf3 -c 192.168.%d.232 -t %d 2>&1 | tee -a %snet%d_temp.log &", tool_path, cmd->exe_param.net_num, cmd->exe_param.net_time, temp_path, cmd->exe_param.net_num);
            }
            else
            {
                sprintf(my_cmd, "%siperf3 -c 192.168.%d.232 -R -t %d 2>&1 | tee -a %snet%d_temp.log &", tool_path, cmd->exe_param.net_num, cmd->exe_param.net_time, temp_path, cmd->exe_param.net_num);
            }
            bsp_my_system(my_cmd, &(cmd->exe_param.pid));
            if (0 == check_command_pid(cmd->exe_param.pid))
            {
                if (cmd->pid_err > PID_ERR_MAX)
                {
                    cmd->exe_param.pid = 0;
                    cmd->state = CURRENT_STATE_OVER;
                    strcpy(err_reson, "can not exe iperf3");
                    bsp_print_save("FAILED", temp_item, err_reson);
                    bsp_save_dmesg(cmd->err_count);
                    cmd->err_count++;
                    bsp_my_system2("ps -ax", 'w');
                    bsp_my_system2("ps", 'w');
                }
                else
                {
                    cmd->pid_err++;
                }
            }
            else
            {
                cmd->pid_err = 0;
                cmd->exe_param.step++;
                cmd->exe_param.start_time = func_get_system_time_ms();
                sprintf(my_cmd, "%siostat %s -x 60 &", tool_path, info.dev);
                bsp_my_system2(my_cmd, 'w');
            }
        }
    }
    break;
    case 2: //
    {
        curr_time = func_get_system_time_ms();
        if (((curr_time - cmd->exe_param.start_time) > (cmd->timeout * 1000)) && (0 == cmd->exe_param.response_flag))
        {
            kill_command(cmd->exe_param.pid);
            cmd->exe_param.pid = 0;
            cmd->state = CURRENT_STATE_OVER;
            strcpy(err_reson, "iperf3 test timeout");
            bsp_print_save("FAILED", temp_item, err_reson);
            bsp_save_dmesg(cmd->err_count);
            memset(my_cmd, 0, sizeof(my_cmd));
            sprintf(my_cmd, "%siostat %s", tool_path, info.dev);
            kill_command_kw(my_cmd);
            cmd->err_count++;
        }
        else
        {
            if (0 != cmd->exe_param.pid)
            {
                if (0 == check_command_pid(cmd->exe_param.pid))
                    cmd->exe_param.pid = 0;
            }
            else
            {
                if (1 == cmd->exe_param.response_flag)
                {
                    cmd->exe_param.step = 0;
                    return;
                }

                // 确认测试结果
                memset(my_cmd, 0, sizeof(my_cmd));
                sprintf(my_cmd, "%siostat %s", tool_path, info.dev);
                kill_command_kw(my_cmd);

                sprintf(temp_file_name, "%snet%d_temp.log", temp_path, cmd->exe_param.net_num);
                tempLogFile = file_open(temp_file_name, ONLY_R);
                if (NULL == tempLogFile)
                {
                    cmd->state = CURRENT_STATE_OVER;
                    strcpy(err_reson, "iperf3 temp file err");
                    bsp_print_save("FAILED", temp_item, err_reson);
                    cmd->err_count++;
                    return;
                }

                fseek(tempLogFile, 0, SEEK_SET);

                while (!feof(tempLogFile))
                {
                    memset(line, 0, sizeof(line));
                    if (fgets(line, sizeof(line) - 1, tempLogFile) != NULL)
                    {
                        if ((NULL != (strstr(line, cmd->exe_param.err_keyword0))) || (NULL != (strstr(line, cmd->exe_param.err_keyword1))))
                        {
                            cmd->state = CURRENT_STATE_OVER;
                            strcpy(err_reson, "iperf3 exe err");
                            bsp_print_save("FAILED", temp_item, err_reson);
                            bsp_save_dmesg(cmd->err_count);
                            cmd->err_count++;
                            have_res = 1;
                            break;
                        }
                        else if (NULL != (p = strstr(line, cmd->exe_param.keyword)))
                        {
                            cmd->state = CURRENT_STATE_OVER;
                            char temp_len[10][20];
                            sscanf(line, "%s %s %s %s %s %s %s %s %s %s", temp_len[0], temp_len[1], temp_len[2], temp_len[3], temp_len[4], temp_len[5], temp_len[6], temp_len[7], temp_len[8], temp_len[9]);
                            cmd->result_data[0].value = atof(temp_len[6]);
                            bsp_data_deal(&(cmd->result_data[0]));
                            cmd->new_data_flag = 1;
                            if (cmd->result_data[0].value > cmd->exe_param.net_threshold)
                            {
                                sprintf(err_reson, "threshold : %.2f,speed : %.2f", cmd->exe_param.net_threshold, cmd->result_data[0].value);
                                bsp_print_save("PASSED", temp_item, err_reson);
                            }
                            else
                            {
                                sprintf(err_reson, "speed err,threshold : %.2f,speed : %.2f", cmd->exe_param.net_threshold, cmd->result_data[0].value);
                                bsp_print_save("FAILED", temp_item, err_reson);
                                cmd->err_count++;
                            }
                            have_res = 1;
                            break;
                        }
                    }
                }
                if (!have_res)
                {
                    cmd->state = CURRENT_STATE_OVER;
                    strcpy(err_reson, "iperf3 exe err");
                    bsp_print_save("FAILED", temp_item, err_reson);
                    bsp_save_dmesg(cmd->err_count);
                    cmd->err_count++;
                }
                fseek(tempLogFile, 0, SEEK_SET);
                file_add(pLogFile, tempLogFile);
                file_close(tempLogFile);
                unlink(temp_file_name);
            }
        }
    }
    break;
    default:
    {
        cmd->state = CURRENT_STATE_OVER;
        strcpy(err_reson, "iperf3 exe err");
        bsp_print_save("FAILED", temp_item, err_reson);
        memset(my_cmd, 0, sizeof(my_cmd));
        sprintf(my_cmd, "%siostat %s", tool_path, info.dev);
        kill_command_kw(my_cmd);
        cmd->err_count++;
        return;
    }
    }
}
/*
 * @description : 任意测试执行模块
 * @param -*cmd : 命令
 * @return		: 无
 */
void func_any_test(struct_test_cmd *cmd)
{
    char temp_item[100] = {0};
    char err_reson[200] = {0};
    char temp_file_name[200] = {0};
    char line[500] = {0};
    FILE *tempLogFile = NULL;
    char *p = NULL;
    int have_res = 0;
    sprintf(temp_item, "%s", cmd->exe_param.dev);
    switch (cmd->exe_param.step)
    {
    case 0:
    {
        if (1 == cmd->exe_param.param_err)
        {
            cmd->state = CURRENT_STATE_OVER;
            sprintf(err_reson, "%s param err", cmd->exe_param.dev);
            bsp_print_save("FAILED", temp_item, err_reson);
            cmd->err_count++;
            return;
        }
        cmd->exe_param.step++;
        cmd->pid_err = 0;
    }
    break;
    case 1:
    {
        if (1 == cmd->exe_param.param_err)
        {
            cmd->state = CURRENT_STATE_OVER;
            sprintf(err_reson, "%s param err", cmd->exe_param.dev);
            bsp_print_save("FAILED", temp_item, err_reson);
            cmd->err_count++;
            return;
        }
        if (1 == cmd->exe_param.response_flag)
        {
            printf("want exe cmd = %s\n", cmd->exe);
            bsp_my_system(cmd->exe, &(cmd->exe_param.pid));
            if (0 == check_command_pid(cmd->exe_param.pid))
            {
                cmd->exe_param.pid = 0;
                cmd->state = CURRENT_STATE_OVER;
                strcpy(err_reson, "can not exe cmd");
                bsp_print_save("FAILED", temp_item, err_reson);
            }
            else
            {
                cmd->exe_param.step++;
            }
        }
        else
        {
            bsp_my_system(cmd->exe, &(cmd->exe_param.pid));
            if (0 == check_command_pid(cmd->exe_param.pid))
            {
                if (cmd->pid_err > PID_ERR_MAX)
                {
                    cmd->exe_param.pid = 0;
                    cmd->state = CURRENT_STATE_OVER;
                    strcpy(err_reson, "can not exe cmd");
                    bsp_print_save("FAILED", temp_item, err_reson);
                    bsp_save_dmesg(cmd->err_count);
                    cmd->err_count++;
                    bsp_my_system2("ps -ax", 'w');
                    bsp_my_system2("ps", 'w');
                }
                else
                {
                    cmd->pid_err++;
                }
            }
            else
            {
                cmd->exe_param.start_time = func_get_system_time_ms(); // 记录开始时间
                cmd->exe_param.step++;
                cmd->pid_err = 0;
            }
        }
    }
    break;
    case 2: //
    {
        if (0 != cmd->timeout)
        {
            unsigned long curr_time = func_get_system_time_ms();
            if ((curr_time - cmd->exe_param.start_time) > (cmd->timeout * 1000))
            {
                cmd->state = CURRENT_STATE_OVER;
                sprintf(err_reson, "%s test timeout %lu s,st=%lu,end=%lu,pid=%d", cmd->exe_param.dev, cmd->timeout, (cmd->exe_param.start_time), curr_time, cmd->exe_param.pid);
                bsp_print_save("FAILED", temp_item, err_reson);
                bsp_save_dmesg(cmd->err_count);
                kill_command(cmd->exe_param.pid);
                cmd->exe_param.pid = 0;
                cmd->err_count++;
                return;
            }
        }
        if (0 != cmd->exe_param.pid)
        {
            if (0 == check_command_pid(cmd->exe_param.pid))
                cmd->exe_param.pid = 0;
        }
        else
        {
            if (1 == cmd->exe_param.response_flag)
            {
                cmd->exe_param.step = 0;
                return;
            }

            // 确认测试结果
            sprintf(temp_file_name, "%s%s_temp.log", temp_path, cmd->exe_param.dev);
            tempLogFile = file_open(temp_file_name, ONLY_R);
            if (NULL == tempLogFile)
            {
                cmd->state = CURRENT_STATE_OVER;
                sprintf(err_reson, "temp file err");
                bsp_print_save("FAILED", temp_item, err_reson);
                cmd->err_count++;
                return;
            }

            fseek(tempLogFile, 0, SEEK_SET);

            while (!feof(tempLogFile))
            {
                memset(line, 0, sizeof(line));
                if (fgets(line, sizeof(line) - 1, tempLogFile) != NULL)
                {
                    if (NULL != (strstr(line, cmd->exe_param.err_keyword0)))
                    {
                        cmd->state = CURRENT_STATE_OVER;
                        strcpy(err_reson, "exe err");
                        bsp_print_save("FAILED", temp_item, err_reson);
                        bsp_save_dmesg(cmd->err_count);
                        cmd->err_count++;
                        have_res = 1;
                        break;
                    }
                    else if (NULL != (p = strstr(line, cmd->exe_param.keyword)))
                    {
                        cmd->state = CURRENT_STATE_OVER;
                        bsp_print_save("PASSED", temp_item, err_reson);
                        have_res = 1;
                        break;
                    }
                }
            }
            if (!have_res)
            {
                cmd->state = CURRENT_STATE_OVER;
                strcpy(err_reson, "exe err");
                bsp_print_save("FAILED", temp_item, err_reson);
                bsp_save_dmesg(cmd->err_count);
                cmd->err_count++;
            }
            file_close(tempLogFile);
            unlink(temp_file_name);
        }
    }
    break;
    default:
    {
        cmd->state = CURRENT_STATE_OVER;
        strcpy(err_reson, "exe err");
        bsp_print_save("FAILED", temp_item, err_reson);
        cmd->err_count++;
        return;
    }
    }
}
/*
 * @description : RTC测试执行模块
 * @param -*cmd : 命令
 * @return		: 无
 */
void func_rtc_test(struct_test_cmd *cmd)
{
    char temp_item[100] = {0};
    char err_reson[100] = {0};
    int fd;
    int ret = 0;
    struct rtc_time rtc_tm_set = {0};
    struct rtc_time rtc_tm_read = {0};

    sprintf(temp_item, "rtc test");

    if (1 == cmd->exe_param.param_err)
    {
        cmd->state = CURRENT_STATE_OVER;
        strcpy(err_reson, "test param err");
        bsp_print_save("FAILED", temp_item, err_reson);
        cmd->err_count++;
        return;
    }
    // 打开RTC设备
    fd = open(cmd->exe_param.dev, O_RDWR | O_CLOEXEC);
    if (fd < 0)
    {
        cmd->state = CURRENT_STATE_OVER;
        strcpy(err_reson, "test param err");
        bsp_print_save("FAILED", temp_item, err_reson);
        cmd->err_count++;
        return;
    }

    // 设置RTC时间为2025年5月1日 12:00:00
    rtc_tm_set.tm_year = 2025 - 1900; // 年份需要减去1900
    rtc_tm_set.tm_mon = 5 - 1;        // 月份从0开始，所以5月是4
    rtc_tm_set.tm_mday = 1;           // 日期
    rtc_tm_set.tm_hour = 12;          // 小时
    rtc_tm_set.tm_min = 0;            // 分钟
    rtc_tm_set.tm_sec = 0;            // 秒

    printf("Setting RTC time to: 2025-05-01 12:00:00\n");

    // 设置RTC时间
    ret = ioctl(fd, RTC_SET_TIME, &rtc_tm_set);
    if (ret < 0)
    {
        cmd->state = CURRENT_STATE_OVER;
        strcpy(err_reson, "failed to set RTC time");
        bsp_print_save("FAILED", temp_item, err_reson);
        cmd->err_count++;
        close(fd);
        return;
    }

    // 等待一小段时间确保设置生效
    usleep(100000); // 100ms

    // 读取RTC时间
    ret = ioctl(fd, RTC_RD_TIME, &rtc_tm_read);
    if (ret < 0)
    {
        cmd->state = CURRENT_STATE_OVER;
        strcpy(err_reson, "failed to read RTC time");
        bsp_print_save("FAILED", temp_item, err_reson);
        cmd->err_count++;
        close(fd);
        return;
    }

    // 打印读取到的时间
    printf("Read RTC time: %04d-%02d-%02d %02d:%02d:%02d\n",
           rtc_tm_read.tm_year + 1900, rtc_tm_read.tm_mon + 1, rtc_tm_read.tm_mday,
           rtc_tm_read.tm_hour, rtc_tm_read.tm_min, rtc_tm_read.tm_sec);

    // 验证时间是否正确设置
    if (rtc_tm_read.tm_year != rtc_tm_set.tm_year ||
        rtc_tm_read.tm_mon != rtc_tm_set.tm_mon ||
        rtc_tm_read.tm_mday != rtc_tm_set.tm_mday)
    {
        cmd->state = CURRENT_STATE_OVER;
        strcpy(err_reson, "RTC Date mismatch");
        bsp_print_save("FAILED", temp_item, err_reson);
        cmd->err_count++;
        close(fd);
        return;
    }

    cmd->state = CURRENT_STATE_OVER;
    bsp_print_save("PASSED", temp_item, err_reson);

    close(fd);
    return;
}
/*
 * @description : 检查NAND变位，坏块数据
 * @param -*cmd : 命令
 * @return		: 无
 */
void func_check_nand_test(struct_test_cmd *cmd)
{
    char temp_item[100] = {0};
    char err_reson[200] = {0};
    char temp_file_name[200] = {0};
    char my_cmd[400] = {0};
    unsigned long curr_time;
    char line[500] = {0};
    FILE *tempLogFile = NULL;
    int have_res = 0;
    int new_ecc_fail = 0, new_ecc_corr = 0, new_bad_block = 0;

    sprintf(temp_item, "check_nand %s", cmd->exe_param.dev);
    switch (cmd->exe_param.step)
    {
    case 0:
    {
        if (1 == cmd->exe_param.param_err)
        {
            cmd->state = CURRENT_STATE_OVER;
            strcpy(err_reson, "check_nand param err");
            bsp_print_save("FAILED", temp_item, err_reson);
            cmd->err_count++;
            return;
        }
        memset(my_cmd, 0, sizeof(my_cmd));
        sprintf(my_cmd, "%scheck_nand %s 2>&1 | tee -a %scheck_nand_temp.log &", item_path, cmd->exe_param.dev, temp_path);
        bsp_my_system(my_cmd, &(cmd->exe_param.pid));
        if (0 == check_command_pid(cmd->exe_param.pid))
        {
            if (cmd->pid_err > PID_ERR_MAX)
            {
                cmd->exe_param.pid = 0;
                cmd->state = CURRENT_STATE_OVER;
                strcpy(err_reson, "can not exe check_nand");
                bsp_print_save("FAILED", temp_item, err_reson);
                bsp_save_dmesg(cmd->err_count);
                cmd->err_count++;
                bsp_my_system2("ps -ax", 'w');
                bsp_my_system2("ps", 'w');
            }
            else
            {
                cmd->pid_err++;
            }
        }
        else
        {
            cmd->pid_err = 0;
            cmd->exe_param.step++;
            cmd->exe_param.start_time = func_get_system_time_ms();
        }
    }
    break;
    case 1: //
    {
        curr_time = func_get_system_time_ms();
        if ((curr_time - cmd->exe_param.start_time) > (cmd->timeout * 1000))
        {
            cmd->state = CURRENT_STATE_OVER;
            sprintf(err_reson, "check_nand test timeout %lu s,st=%lu,end=%lu,pid=%d", cmd->timeout, (cmd->exe_param.start_time), curr_time, cmd->exe_param.pid);
            bsp_print_save("FAILED", temp_item, err_reson);
            bsp_save_dmesg(cmd->err_count);
            kill_command(cmd->exe_param.pid);
            cmd->exe_param.pid = 0;
            cmd->err_count++;
        }
        else
        {
            if (0 != cmd->exe_param.pid)
            {
                if (0 == check_command_pid(cmd->exe_param.pid))
                    cmd->exe_param.pid = 0;
            }
            else
            {
                // 确认测试结果
                sprintf(temp_file_name, "%scheck_nand_temp.log", temp_path);
                tempLogFile = file_open(temp_file_name, ONLY_R);
                if (NULL == tempLogFile)
                {
                    cmd->state = CURRENT_STATE_OVER;
                    strcpy(err_reson, "check_nand temp file err");
                    bsp_print_save("FAILED", temp_item, err_reson);
                    cmd->err_count++;
                    return;
                }

                fseek(tempLogFile, 0, SEEK_SET);
                char temp_len[30][5];
                while (!feof(tempLogFile))
                {
                    memset(line, 0, sizeof(line));
                    if (fgets(line, sizeof(line) - 1, tempLogFile) != NULL)
                    {
                        if (NULL != (strstr(line, "ECC failed")))
                        {
                            sscanf(line, "%s %s %s", temp_len[0], temp_len[1], temp_len[2]);
                            new_ecc_fail = atoi(temp_len[2]);
                        }
                        if (NULL != (strstr(line, "ECC corrected")))
                        {
                            sscanf(line, "%s %s %s", temp_len[0], temp_len[1], temp_len[2]);
                            new_ecc_corr = atoi(temp_len[2]);
                        }
                        if (NULL != (strstr(line, "Number of bad blocks")))
                        {
                            sscanf(line, "%s %s %s %s %s", temp_len[0], temp_len[1], temp_len[2], temp_len[3], temp_len[4]);
                            new_bad_block = atoi(temp_len[4]);
                        }
                    }
                }
                if ((((new_ecc_fail > cmd->exe_param.ecc_fail) && ((new_ecc_fail - cmd->exe_param.ecc_fail) > cmd->exe_param.ecc_fial_threshold)) ||
                     ((new_ecc_corr > cmd->exe_param.ecc_corr) && ((new_ecc_corr - cmd->exe_param.ecc_corr) > cmd->exe_param.ecc_corr_threshold)) ||
                     ((new_bad_block > cmd->exe_param.bad_block) && ((new_bad_block - cmd->exe_param.bad_block) > cmd->exe_param.bad_block_threshold))) &&
                    (0 != info.circle_count))
                {
                    cmd->state = CURRENT_STATE_OVER;
                    sprintf(err_reson, "ecc_fail : %d,%d;ecc_corr : %d,%d;bad_block : %d,%d", new_ecc_fail, cmd->exe_param.ecc_fail, new_ecc_corr, cmd->exe_param.ecc_corr, new_bad_block, cmd->exe_param.bad_block);
                    bsp_print_save("FAILED", temp_item, err_reson);
                    bsp_save_dmesg(cmd->err_count);
                    cmd->err_count++;
                    have_res = 1;
                    // break;
                }
                else
                {
                    bsp_clean_caches();
                    char nand_file_temp_md5[33] = {0};
                    char nand_temp_file_name[200] = {0};
                    sprintf(nand_temp_file_name, "%s/nand_test_file.bin", temp_path);
                    // 获取文件md5值
                    sprintf(nand_file_temp_md5, "%s", bsp_get_md5_value(nand_temp_file_name));
                    if (strncmp(nand_file_temp_md5, cmd->exe_param.nand_file_md5, 32) != 0)
                    {
                        cmd->state = CURRENT_STATE_OVER;
                        sprintf(err_reson, "check_nand md5 err");
                        bsp_print_save("FAILED", temp_item, err_reson);
                        bsp_save_dmesg(cmd->err_count);
                        cmd->err_count++;
                    }
                    else
                    {
                        cmd->state = CURRENT_STATE_OVER;
                        bsp_print_save("PASSED", temp_item, err_reson);
                    }
                    have_res = 1;
                    // break;
                }
                cmd->exe_param.ecc_fail = new_ecc_fail;
                cmd->exe_param.ecc_corr = new_ecc_corr;
                cmd->exe_param.bad_block = new_bad_block;

                if (!have_res)
                {
                    cmd->state = CURRENT_STATE_OVER;
                    strcpy(err_reson, "check_nand exe err");
                    bsp_print_save("FAILED", temp_item, err_reson);
                    bsp_save_dmesg(cmd->err_count);
                    cmd->err_count++;
                }
                fseek(tempLogFile, 0, SEEK_SET);
                file_add(pLogFile, tempLogFile);
                file_close(tempLogFile);
                unlink(temp_file_name);
            }
        }
    }
    break;
    default:
    {
        cmd->state = CURRENT_STATE_OVER;
        strcpy(err_reson, "check_nand exe err");
        bsp_print_save("FAILED", temp_item, err_reson);
        cmd->err_count++;
        return;
    }
    }
}

/*
 * @description : 响应测试管理线程
 * @param - arg : 参数
 * @return		: 无
 */
void *response_test_manage(void *arg)
{
    struct_test *current;
    int i = 0, j = 0, k = 0;
    struct pollfd pollfds[2] = {{response_signal_fd[1], POLLIN, 0}};
    int ne, ret, nevents = 1;
    printf("response test pthread start\n");

    current = (struct_test *)arg;
    for (i = 0; i < MAX_TEST_ITEM; i++)
    {
        if ((i == CAN) || (i == RS485) || (i == RS232) || (i == NET))
        {
            if (current->item[i].circle != 0)
            {
                for (j = 0; j < current->item[i].groups; j++)
                {
                    for (k = 0; k < current->item[i].group[j].numb; k++)
                    {
                        current->item[i].group[j].cmd[k].state = CURRENT_STATE_RUN;
                        current->item[i].group[j].cmd[k].exe_param.step = 0;
                        current->item[i].group[j].cmd[k].exe_param.pid = 0;
                        current->item[i].group[j].cmd[k].exe_param.response_flag = 1;
                    }
                }
                current->item[i].circle = 0; // 为了不让后续测试在启动
            }
        }
    }
    while (1)
    {
        do
        {
            ret = poll(pollfds, nevents, 1 * 500);
        } while ((ret < 0) && (errno == EINTR));
        if (ret < 0)
        {
            printf("%s poll=%d, errno: %d (%s)", __func__, ret, errno, strerror(errno));
            break;
        }
        if (ret == 0)
        {
            for (i = 0; i < MAX_TEST_ITEM; i++)
            {
                if ((i == CAN) || (i == RS485) || (i == RS232) || (i == NET))
                {
                    for (j = 0; j < current->item[i].groups; j++)
                    {
                        for (k = 0; k < current->item[i].group[j].numb; k++)
                        {
                            if (1 == current->item[i].group[j].cmd[k].exe_param.response_flag)
                            {
                                pthread_mutex_lock(&mutex_lock);
                                switch (current->item[i].group[j].cmd[k].exe_param.mode)
                                {
                                case ANY_MODE:
                                {
                                    func_any_test(&(current->item[i].group[j].cmd[k]));
                                }
                                break;
                                case NET_MODE: //
                                {
                                    func_net_test(&(current->item[i].group[j].cmd[k]));
                                }
                                break;
                                default:
                                {
                                    break;
                                }
                                }
                                pthread_mutex_unlock(&mutex_lock);
                            }
                        }
                    }
                }
            }
        }
        else
        {
            for (ne = 0; ne < nevents; ne++)
            {
                int fd_temp = pollfds[ne].fd;
                short revents = pollfds[ne].revents;

                if (revents & (POLLERR | POLLHUP | POLLNVAL))
                    goto __response_test_Thread_quit;

                if ((revents & POLLIN) == 0)
                    continue;
                if (fd_temp == response_signal_fd[1])
                {
                    int triger_event;
                    if (read(fd_temp, &triger_event, sizeof(triger_event)) == sizeof(triger_event))
                    {
                        if (QUITE_EVENT == triger_event)
                            goto __response_test_Thread_quit;
                    }
                }
            }
        }
    }
__response_test_Thread_quit:
    printf("%s exit\n", __func__);
    for (i = 0; i < MAX_TEST_ITEM; i++)
    {
        if ((i == CAN) || (i == RS485) || (i == RS232) || (i == NET))
        {
            for (j = 0; j < current->item[i].groups; j++)
            {
                for (k = 0; k < current->item[i].group[j].numb; k++)
                {
                    if (0 != current->item[i].group[j].cmd[k].exe_param.pid)
                    {
                        kill_command(current->item[i].group[j].cmd[k].exe_param.pid);
                        current->item[i].group[j].cmd[k].exe_param.pid = 0;
                    }
                }
            }
        }
    }
    close(response_signal_fd[0]);
    close(response_signal_fd[1]);
    pthread_exit(NULL);
    return NULL;
}
/*
 * @description : 并行执行测试管理线程
 * @param - arg : 参数
 * @return		: 无
 */
void *concurrent_test_manage(void *arg)
{
    struct_test_item *current;
    // struct timeval Time;
    int i = 0, flag = 0, circles = 0;

    current = (struct_test_item *)arg;
    current->count = 0;
    current->cur_groups = 0;
    current->state = RUNNING;
    struct pollfd pollfds[2] = {{concurrent_test_signal_fd[current->numb][1], POLLIN, 0}};
    int ne, ret, nevents = 1;

    if (-1 == current->circle)
        circles = 1;
    else
        circles = current->circle;
    while (current->count < circles)
    {
        file_write_subitem_head(pLogFile, &info, current);

        while (current->cur_groups <= current->groups)
        {
            for (i = 0; i < current->group[current->cur_groups].numb; i++)
            {
                current->group[current->cur_groups].cmd[i].state = CURRENT_STATE_RUN;
                current->group[current->cur_groups].cmd[i].exe_param.step = 0;
                current->group[current->cur_groups].cmd[i].exe_param.pid = 0;
            }
            while (1)
            {
                do
                {
                    ret = poll(pollfds, nevents, 1 * 100);
                } while ((ret < 0) && (errno == EINTR));
                if (ret < 0)
                {
                    printf("%s poll=%d, errno: %d (%s)", __func__, ret, errno, strerror(errno));
                    goto __concurrent_test_Thread_recovery;
                }
                if (ret == 0)
                {
                    for (i = 0; i < current->group[current->cur_groups].numb; i++)
                    {
                        pthread_mutex_lock(&mutex_lock);
                        if (CURRENT_STATE_RUN == current->group[current->cur_groups].cmd[i].state)
                        {
                            switch (current->group[current->cur_groups].cmd[i].exe_param.mode)
                            {
                            case ANY_MODE:
                            {
                                func_any_test(&(current->group[current->cur_groups].cmd[i]));
                            }
                            break;
                            case DD_MODE: //
                            {
                                func_dd_test(&(current->group[current->cur_groups].cmd[i]));
                            }
                            break;
                            case MEMTER_MODE: //
                            {
                                func_memter_test(&(current->group[current->cur_groups].cmd[i]), i, current->group[current->cur_groups].memtester_count);
                            }
                            break;
                            case MEM_BW_MODE: //
                            {
                                func_mem_bw_test(&(current->group[current->cur_groups].cmd[i]));
                            }
                            break;
                            case RTC_TEST_MODE: //
                            {
                                func_rtc_test(&(current->group[current->cur_groups].cmd[i]));
                            }
                            break;
                            case CHECK_NAND_ERR: //
                            {
                                func_check_nand_test(&(current->group[current->cur_groups].cmd[i]));
                            }
                            break;
                            case NET_MODE: //
                            {
                                func_net_test(&(current->group[current->cur_groups].cmd[i]));
                            }
                            break;
                            default:
                            {
                                func_any_test(&(current->group[current->cur_groups].cmd[i]));
                            }
                            }
                        }
                        pthread_mutex_unlock(&mutex_lock);
                    }
                }
                else
                {
                    for (ne = 0; ne < nevents; ne++)
                    {
                        int fd_temp = pollfds[ne].fd;
                        short revents = pollfds[ne].revents;

                        if (revents & (POLLERR | POLLHUP | POLLNVAL))
                            goto __concurrent_test_Thread_recovery;

                        if (fd_temp == concurrent_test_signal_fd[current->numb][1])
                        {
                            int triger_event;
                            if (read(fd_temp, &triger_event, sizeof(triger_event)) == sizeof(triger_event))
                            {
                                if (QUITE_EVENT == triger_event)
                                {
                                    goto __concurrent_test_Thread_recovery;
                                }
                            }
                        }
                    }
                }
                flag = 0;
                for (i = 0; i < current->group[current->cur_groups].numb; i++)
                {
                    if (CURRENT_STATE_RUN == current->group[current->cur_groups].cmd[i].state)
                    {
                        flag = 1;
                        break;
                    }
                }
                if (0 == flag)
                    break;
            }
            current->cur_groups++;
        }
        if (-1 != current->circle)
            current->count++;
    }
    current->state = OVER;
    goto __concurrent_test_Thread_quit;

__concurrent_test_Thread_recovery:
    printf("%s exit", __func__);
    for (i = 0; i < current->group[current->cur_groups].numb; i++)
    {
        if (0 != current->group[current->cur_groups].cmd[i].exe_param.pid)
        {
            kill_command(current->group[current->cur_groups].cmd[i].exe_param.pid);
            current->group[current->cur_groups].cmd[i].exe_param.pid = 0;
        }
    }
    close(concurrent_test_signal_fd[current->numb][0]);
    close(concurrent_test_signal_fd[current->numb][1]);
__concurrent_test_Thread_quit:
    pthread_exit(NULL);
    return NULL;
}
/*
 * @description : 顺序执行测试管理线程
 * @param - arg : 参数
 * @return		: 无
 */
void *in_order_test_manage(void *arg)
{
    struct_test_item *current;
    int i = 0, circles = 0, flag = 0;

    current = (struct_test_item *)arg;
    current->count = 0;
    struct pollfd pollfds[2] = {{order_test_signal_fd[1], POLLIN, 0}};
    int ne, ret, nevents = 1;
    current->state = RUNNING;
    // printf("in order test pthread start\n");

    if (-1 == current->circle)
        circles = 1;
    else
        circles = current->circle;

    while (current->count < circles)
    {
        file_write_subitem_head(pLogFile, &info, current);
        current->cur_groups = 0;

        while (current->cur_groups <= current->groups)
        {
            for (i = 0; i < current->group[current->cur_groups].numb; i++)
            {
                current->group[current->cur_groups].cmd[i].state = CURRENT_STATE_RUN;
                current->group[current->cur_groups].cmd[i].exe_param.step = 0;
                current->group[current->cur_groups].cmd[i].exe_param.pid = 0;
            }
            while (1)
            {
                do
                {
                    ret = poll(pollfds, nevents, 1 * 400);
                } while ((ret < 0) && (errno == EINTR));
                if (ret < 0)
                {
                    printf("%s poll=%d, errno: %d (%s)", __func__, ret, errno, strerror(errno));
                    goto __order_test_Thread_recovery;
                }
                if (ret == 0)
                {
                    for (i = 0; i < current->group[current->cur_groups].numb; i++)
                    {
                        pthread_mutex_lock(&mutex_lock);
                        if (CURRENT_STATE_RUN == current->group[current->cur_groups].cmd[i].state)
                        {
                            switch (current->group[current->cur_groups].cmd[i].exe_param.mode)
                            {
                            case ANY_MODE:
                            {
                                func_any_test(&(current->group[current->cur_groups].cmd[i]));
                            }
                            break;
                            case DD_MODE: //
                            {
                                func_dd_test(&(current->group[current->cur_groups].cmd[i]));
                            }
                            break;
                            case MEMTER_MODE: //
                            {
                                func_memter_test(&(current->group[current->cur_groups].cmd[i]), i, current->group[current->cur_groups].memtester_count);
                            }
                            break;
                            case MEM_BW_MODE: //
                            {
                                func_mem_bw_test(&(current->group[current->cur_groups].cmd[i]));
                            }
                            break;
                            case RTC_TEST_MODE: //
                            {
                                func_rtc_test(&(current->group[current->cur_groups].cmd[i]));
                            }
                            break;
                            case CHECK_NAND_ERR: //
                            {
                                func_check_nand_test(&(current->group[current->cur_groups].cmd[i]));
                            }
                            break;
                            case NET_MODE: //
                            {
                                func_net_test(&(current->group[current->cur_groups].cmd[i]));
                            }
                            break;
                            default:
                            {
                                func_any_test(&(current->group[current->cur_groups].cmd[i]));
                            }
                            }
                        }
                        pthread_mutex_unlock(&mutex_lock);
                    }
                }
                else
                {
                    // printf("step 100\n");
                    for (ne = 0; ne < nevents; ne++)
                    {
                        int fd_temp = pollfds[ne].fd;
                        short revents = pollfds[ne].revents;

                        if (revents & (POLLERR | POLLHUP | POLLNVAL))
                            goto __order_test_Thread_recovery;

                        if (fd_temp == order_test_signal_fd[1])
                        {
                            int triger_event;
                            if (read(fd_temp, &triger_event, sizeof(triger_event)) == sizeof(triger_event))
                            {
                                if (QUITE_EVENT == triger_event)
                                {
                                    goto __order_test_Thread_recovery;
                                }
                            }
                        }
                    }
                }
                flag = 0;
                for (i = 0; i < current->group[current->cur_groups].numb; i++)
                {
                    if (CURRENT_STATE_RUN == current->group[current->cur_groups].cmd[i].state)
                    {
                        flag = 1;
                        break;
                    }
                }
                if (0 == flag)
                    break;
            }
            current->cur_groups++;
        }
        if (-1 != current->circle)
            current->count++;
    }
    current->state = OVER;
    goto __order_test_Thread_quit;

__order_test_Thread_recovery:
    printf("%s exit\n", __func__);
    for (i = 0; i < current->group[current->cur_groups].numb; i++)
    {
        if (0 != current->group[current->cur_groups].cmd[i].exe_param.pid)
        {
            printf("want kill pid=%d\n", current->group[current->cur_groups].cmd[i].exe_param.pid);
            kill_command(current->group[current->cur_groups].cmd[i].exe_param.pid);
            current->group[current->cur_groups].cmd[i].exe_param.pid = 0;
        }
    }
    close(order_test_signal_fd[0]);
    close(order_test_signal_fd[1]);
__order_test_Thread_quit:
    pthread_exit(NULL);
    return NULL;
}
/*
 * @description      : 打印本轮测试结果
 * @param - *manag   : 测试管理结构体
 * @param - *infos   : 测试信息结构体
 * @return		     : 无
 */
void Func_print_result(struct_test *manag, struct_test_info *infos)
{
    int i = 0, j = 0, k = 0;
    char name[10] = {0};

    printf(YELLOW "Results preview circle_count : %d ,", infos->circle_count);
    for (i = 0; i < MAX_TEST_ITEM; i++)
    {
        if (manag->item[i].circle)
        {
            for (j = 0; j < manag->item[i].groups; j++)
            {
                for (k = 0; k < manag->item[i].group[j].numb; k++)
                {
                    memset(name, 0, sizeof(name));
                    get_test_name(i, name);
                    switch (manag->item[i].group[j].cmd[k].exe_param.mode)
                    {
                    case ANY_MODE:
                    {
                        printf("%s_%s : %d ,", name, manag->item[i].group[j].cmd[k].exe_param.dev, manag->item[i].group[j].cmd[k].err_count);
                    }
                    break;
                    case DD_MODE: //
                    {
                        printf("%s_dd_%s : %d ,", name, manag->item[i].group[j].cmd[k].exe_param.dd_path, manag->item[i].group[j].cmd[k].err_count);
                    }
                    break;
                    case MEMTER_MODE: //
                    {
                        printf("%s_memtester%d : %d ,", name, k, manag->item[i].group[j].cmd[k].err_count);
                    }
                    break;
                    case MEM_BW_MODE: //
                    {
                        printf("%s_mem_bw : %d ,", name, manag->item[i].group[j].cmd[k].err_count);
                    }
                    break;
                    case RTC_TEST_MODE: //
                    {
                        printf("%s_rtc : %d ,", name, manag->item[i].group[j].cmd[k].err_count);
                    }
                    break;
                    case CHECK_NAND_ERR:
                    {
                        printf("%s_check_nand : %d ,", name, manag->item[i].group[j].cmd[k].err_count);
                    }
                    break;
                    case NET_MODE: //
                    {
                        printf("%s_iperf3_net%d : %d ,", name, manag->item[i].group[j].cmd[k].exe_param.net_num, manag->item[i].group[j].cmd[k].err_count);
                    }
                    break;
                    default:
                    {
                        break;
                    }
                    }
                }
            }
        }
    }
    printf(NONE "\n");
}
/*
 * @description      : 生成测试报告
 * @param - *pFile   : 测试报告文件
 * @param - *manag   : 测试管理结构体
 * @param - *infos   : 测试信息结构体
 * @return		     : 无
 */
void Func_test_report(FILE *pFile, struct_test *manag, struct_test_info *infos)
{
    int i = 0, j = 0, k = 0;
    char name[10] = {0};
    char data[300] = {0};

    for (i = 0; i < MAX_TEST_ITEM; i++)
    {
        if (manag->item[i].circle)
        {
            for (j = 0; j < manag->item[i].groups; j++)
            {
                for (k = 0; k < manag->item[i].group[j].numb; k++)
                {
                    memset(name, 0, sizeof(name));
                    get_test_name(i, name);
                    switch (manag->item[i].group[j].cmd[k].exe_param.mode)
                    {
                    case ANY_MODE:
                    {
                        memset(data, 0, sizeof(data));
                        sprintf(data, "%s,%s,%s,%s\n", "item", "circle", "passed", "failed");
                        fwrite(data, sizeof(char), strlen(data), pFile);
                        memset(data, 0, sizeof(data));
                        sprintf(data, "%s_%s,%d,%d,%d\n\n", name, manag->item[i].group[j].cmd[k].exe_param.dev, infos->circle_count, (infos->circle_count - manag->item[i].group[j].cmd[k].err_count), manag->item[i].group[j].cmd[k].err_count);
                        fwrite(data, sizeof(char), strlen(data), pFile);
                    }
                    break;
                    case DD_MODE: //
                    {
                        memset(data, 0, sizeof(data));
                        sprintf(data, "%s,%s,%s,%s\n", "item", "circle", "passed", "failed");
                        fwrite(data, sizeof(char), strlen(data), pFile);
                        memset(data, 0, sizeof(data));
                        sprintf(data, "%s_dd_%s,%d,%d,%d\n", name, manag->item[i].group[j].cmd[k].exe_param.dd_path, infos->circle_count, (infos->circle_count - manag->item[i].group[j].cmd[k].err_count), manag->item[i].group[j].cmd[k].err_count);
                        fwrite(data, sizeof(char), strlen(data), pFile);
                        memset(data, 0, sizeof(data));
                        sprintf(data, "%s,%s,%s,%s\n", "write_speed", "max", "min", "average");
                        fwrite(data, sizeof(char), strlen(data), pFile);
                        memset(data, 0, sizeof(data));
                        sprintf(data, "%s,%.2f,%.2f,%.2f\n", "---", manag->item[i].group[j].cmd[k].result_data[0].max_value, manag->item[i].group[j].cmd[k].result_data[0].min_value, manag->item[i].group[j].cmd[k].result_data[0].aver_value);
                        fwrite(data, sizeof(char), strlen(data), pFile);
                        memset(data, 0, sizeof(data));
                        sprintf(data, "%s,%d~%d,%d~%d,%d~%d,%d~%d,%d~\n", "range", 0, manag->item[i].group[j].cmd[k].result_data[0].stage_param,
                                manag->item[i].group[j].cmd[k].result_data[0].stage_param, (manag->item[i].group[j].cmd[k].result_data[0].stage_param * 2),
                                (manag->item[i].group[j].cmd[k].result_data[0].stage_param * 2), (manag->item[i].group[j].cmd[k].result_data[0].stage_param * 3),
                                (manag->item[i].group[j].cmd[k].result_data[0].stage_param * 3), (manag->item[i].group[j].cmd[k].result_data[0].stage_param * 4),
                                (manag->item[i].group[j].cmd[k].result_data[0].stage_param * 4));
                        fwrite(data, sizeof(char), strlen(data), pFile);
                        memset(data, 0, sizeof(data));
                        sprintf(data, "%s,%d,%d,%d,%d,%d\n", "---", manag->item[i].group[j].cmd[k].result_data[0].stage_count[0], manag->item[i].group[j].cmd[k].result_data[0].stage_count[1], manag->item[i].group[j].cmd[k].result_data[0].stage_count[2],
                                manag->item[i].group[j].cmd[k].result_data[0].stage_count[3], manag->item[i].group[j].cmd[k].result_data[0].stage_count[4]);
                        fwrite(data, sizeof(char), strlen(data), pFile);

                        memset(data, 0, sizeof(data));
                        sprintf(data, "%s,%s,%s,%s\n", "read_speed", "max", "min", "average");
                        fwrite(data, sizeof(char), strlen(data), pFile);
                        memset(data, 0, sizeof(data));
                        sprintf(data, "%s,%.2f,%.2f,%.2f\n", "---", manag->item[i].group[j].cmd[k].result_data[1].max_value, manag->item[i].group[j].cmd[k].result_data[1].min_value, manag->item[i].group[j].cmd[k].result_data[1].aver_value);
                        fwrite(data, sizeof(char), strlen(data), pFile);
                        memset(data, 0, sizeof(data));
                        sprintf(data, "%s,%d~%d,%d~%d,%d~%d,%d~%d,%d~\n", "range", 0, manag->item[i].group[j].cmd[k].result_data[1].stage_param,
                                manag->item[i].group[j].cmd[k].result_data[1].stage_param, (manag->item[i].group[j].cmd[k].result_data[1].stage_param * 2),
                                (manag->item[i].group[j].cmd[k].result_data[1].stage_param * 2), (manag->item[i].group[j].cmd[k].result_data[1].stage_param * 3),
                                (manag->item[i].group[j].cmd[k].result_data[1].stage_param * 3), (manag->item[i].group[j].cmd[k].result_data[1].stage_param * 4),
                                (manag->item[i].group[j].cmd[k].result_data[1].stage_param * 4));
                        fwrite(data, sizeof(char), strlen(data), pFile);
                        memset(data, 0, sizeof(data));
                        sprintf(data, "%s,%d,%d,%d,%d,%d\n\n", "---", manag->item[i].group[j].cmd[k].result_data[1].stage_count[0], manag->item[i].group[j].cmd[k].result_data[1].stage_count[1], manag->item[i].group[j].cmd[k].result_data[1].stage_count[2],
                                manag->item[i].group[j].cmd[k].result_data[1].stage_count[3], manag->item[i].group[j].cmd[k].result_data[1].stage_count[4]);
                        fwrite(data, sizeof(char), strlen(data), pFile);
                    }
                    break;
                    case MEMTER_MODE: //
                    {
                        memset(data, 0, sizeof(data));
                        sprintf(data, "%s,%s,%s,%s\n", "item", "circle", "passed", "failed");
                        fwrite(data, sizeof(char), strlen(data), pFile);
                        memset(data, 0, sizeof(data));
                        sprintf(data, "%s_memtester%d,%d,%d,%d\n\n", name, k, infos->circle_count, (infos->circle_count - manag->item[i].group[j].cmd[k].err_count), manag->item[i].group[j].cmd[k].err_count);
                        fwrite(data, sizeof(char), strlen(data), pFile);
                    }
                    break;
                    case MEM_BW_MODE: //
                    {
                        memset(data, 0, sizeof(data));
                        sprintf(data, "%s,%s,%s,%s\n", "item", "circle", "passed", "failed");
                        fwrite(data, sizeof(char), strlen(data), pFile);
                        memset(data, 0, sizeof(data));
                        sprintf(data, "%s_mem_bw,%d,%d,%d\n\n", name, infos->circle_count, (infos->circle_count - manag->item[i].group[j].cmd[k].err_count), manag->item[i].group[j].cmd[k].err_count);
                        fwrite(data, sizeof(char), strlen(data), pFile);
                    }
                    break;
                    case RTC_TEST_MODE: //
                    {
                        memset(data, 0, sizeof(data));
                        sprintf(data, "%s,%s,%s,%s\n", "item", "circle", "passed", "failed");
                        fwrite(data, sizeof(char), strlen(data), pFile);
                        memset(data, 0, sizeof(data));
                        sprintf(data, "%s_rtc,%d,%d,%d\n\n", name, infos->circle_count, (infos->circle_count - manag->item[i].group[j].cmd[k].err_count), manag->item[i].group[j].cmd[k].err_count);
                        fwrite(data, sizeof(char), strlen(data), pFile);
                    }
                    break;
                    case CHECK_NAND_ERR:
                    {
                        memset(data, 0, sizeof(data));
                        sprintf(data, "%s,%s,%s,%s\n", "item", "circle", "passed", "failed");
                        fwrite(data, sizeof(char), strlen(data), pFile);
                        memset(data, 0, sizeof(data));
                        sprintf(data, "%s_check_nand,%d,%d,%d\n\n", name, infos->circle_count, (infos->circle_count - manag->item[i].group[j].cmd[k].err_count), manag->item[i].group[j].cmd[k].err_count);
                        fwrite(data, sizeof(char), strlen(data), pFile);
                    }
                    break;
                    case NET_MODE: //
                    {
                        memset(data, 0, sizeof(data));
                        sprintf(data, "%s,%s,%s,%s\n", "item", "circle", "passed", "failed");
                        fwrite(data, sizeof(char), strlen(data), pFile);
                        memset(data, 0, sizeof(data));
                        sprintf(data, "%s_iperf3_net%d,%d,%d,%d\n", name, manag->item[i].group[j].cmd[k].exe_param.net_num, infos->circle_count, (infos->circle_count - manag->item[i].group[j].cmd[k].err_count), manag->item[i].group[j].cmd[k].err_count);
                        fwrite(data, sizeof(char), strlen(data), pFile);
                        memset(data, 0, sizeof(data));
                        sprintf(data, "%s,%s,%s,%s\n", "iperf_speed", "max", "min", "average");
                        fwrite(data, sizeof(char), strlen(data), pFile);
                        memset(data, 0, sizeof(data));
                        sprintf(data, "%s,%.2f,%.2f,%.2f\n", "---", manag->item[i].group[j].cmd[k].result_data[0].max_value, manag->item[i].group[j].cmd[k].result_data[0].min_value, manag->item[i].group[j].cmd[k].result_data[0].aver_value);
                        fwrite(data, sizeof(char), strlen(data), pFile);
                        memset(data, 0, sizeof(data));
                        sprintf(data, "%s,%d~%d,%d~%d,%d~%d,%d~%d,%d~\n", "range", 0, manag->item[i].group[j].cmd[k].result_data[0].stage_param,
                                manag->item[i].group[j].cmd[k].result_data[0].stage_param, (manag->item[i].group[j].cmd[k].result_data[0].stage_param * 2),
                                (manag->item[i].group[j].cmd[k].result_data[0].stage_param * 2), (manag->item[i].group[j].cmd[k].result_data[0].stage_param * 3),
                                (manag->item[i].group[j].cmd[k].result_data[0].stage_param * 3), (manag->item[i].group[j].cmd[k].result_data[0].stage_param * 4),
                                (manag->item[i].group[j].cmd[k].result_data[0].stage_param * 4));
                        fwrite(data, sizeof(char), strlen(data), pFile);
                        memset(data, 0, sizeof(data));
                        sprintf(data, "%s,%d,%d,%d,%d,%d\n\n", "---", manag->item[i].group[j].cmd[k].result_data[0].stage_count[0], manag->item[i].group[j].cmd[k].result_data[0].stage_count[1], manag->item[i].group[j].cmd[k].result_data[0].stage_count[2],
                                manag->item[i].group[j].cmd[k].result_data[0].stage_count[3], manag->item[i].group[j].cmd[k].result_data[0].stage_count[4]);
                        fwrite(data, sizeof(char), strlen(data), pFile);
                    }
                    break;
                    default:
                    {
                        break;
                    }
                    }
                }
            }
        }
    }
}

/*
 * @description : 顺序执行时，获取运行状态，获取下一个要执行命令的位置
 * @param - *status  : 运行状态
 * @param - *current : 当前测试条目
 * @param - *manag   : 测试管理结构体
 * @param - *infos   : 测试信息结构体
 * @return		     : -1 ：测试结束，0：继续测试
 */
void get_order_test_status(int *status, int *current, struct_test *manag, struct_test_info *infos)
{
    int i = 0;
    int point = 0;
    unsigned long present_time = 0;

    point = (*current) + 1;

    if (*current == 0)
    {
        file_write_head(pLogFile, infos);
        manag->start_time = func_get_system_time_ms();
        *current = 1;
    }
    if (NOTHING_TO_DO == *status)
        return;
    if (WAITING == *status)
    {
        present_time = func_get_system_time_ms();
        if ((present_time - manag->start_time) < (manag->interval * 1000))
        {
            *status = WAITING;
            return;
        }
        else
        {
            if ((RESPONSE_IN_ORDER == manag->pattern) && (manag->B_can_next_circle == 0))
                return;
            else
                goto __next_circle;
        }
    }
    if (RUNNING == manag->item[*current].state)
    {

        *status = RUNNING;
        return;
    }
    // 寻找下一个测试项
    for (i = point; i < MAX_TEST_ITEM; i++)
    {
        if (manag->item[i].circle != 0)
        {
            manag->item[i].count = 0;
            manag->item[i].numb = i;
            *current = i;
            *status = IDLE;
            return;
        }
    }
    // 执行到这里说明本轮测试项完成，确认是否需要延时等待
    // 增加打印所有测试项本轮测试结果
    Func_print_result(manag, infos);
    present_time = func_get_system_time_ms();
    if ((present_time - manag->start_time) < (manag->interval * 1000))
    {
        *status = WAITING;
        return;
    }
    if ((RESPONSE_IN_ORDER == manag->pattern) && (manag->B_can_next_circle == 0))
    {
        *status = WAITING;
        return;
    }
    // 执行到这里说明要进行下一轮测试
__next_circle:
    if ((SPONSOR_IN_ORDER == test->pattern) && (manag->interact_flag == 1))
        func_tcp_send(sockfd, "B can exe next circle", strlen("B can exe next circle"));
    manag->B_can_next_circle = 0;
    infos->circle_count++;
    file_write_head(pLogFile, infos);

    if ((-1 != manag->circles) && (infos->circle_count >= manag->circles))
    {
        file_write_time_fmt(pLogFile, "Complete all %d circles of testing", infos->circle_count);
        if (SPONSOR_IN_ORDER == test->pattern)
            *status = OVER;
        else
            *status = NOTHING_TO_DO;
        return;
    }
    else
    {
        for (i = 1; i < MAX_TEST_ITEM; i++)
        {
            if (manag->item[i].circle != 0)
            {
                manag->item[i].count = 0;
                *current = i;
                *status = IDLE;
                manag->start_time = func_get_system_time_ms();
                return;
            }
        }
    }
    return;
}
/*
 * @description     : 获得并发所有测试项的运行状态
 * @param - *all    : 测试管理结构体
 * @return		    : 状态
 */
void get_concurrent_test_status(struct_test *all, int *status)
{
    int i = 0;

    if (0 == *status)
    {
        *status = IDLE;
        return;
    }

    for (i = 0; i < MAX_TEST_ITEM; i++)
    {
        if (CURRENT_STATE_RUN == all->item->state)
        {
            *status = RUNNING;
            return;
        }
    }
    if (all->pattern == SPONSOR_CONCURRENT)
        *status = OVER;
    else
        *status = RUNNING;
    return;
}

/*
 * @description   : 获取启动次数
 * @param - *path : 文件路径
 * @param - *count：计数指针
 * @return		  : 0-成功 1-失败
 */
int get_start_count(char *path, int *count)
{
    FILE *f;
    char temp[10] = {0};
    if ((access(path, 0)) != -1)
    {
        f = fopen(path, "r+");
        if (NULL == f)
            return -1;
        else
        {
            file_read(f, temp, 9, 0);
            *count = atoi(temp);
            *count += 1;
            fclose(f);
            f = fopen(path, "w+");
            fprintf(f, "%d\n", *count);
            fclose(f);
        }
    }
    else
    {
        f = fopen(path, "w+");
        if (NULL == f)
            return -1;
        *count = 1;
        fprintf(f, "%d\n", *count);
        fclose(f);
    }
    return 0;
}
/*
 * @description   : 获取cpu版本信息
 * @param - *path : 文件路径
 * @param - *count：计数指针
 * @return		  : 0-成功 1-失败
 */
int get_cpu_info(char *path, char *cpu_info)
{
    if ((access(path, 0)) != -1)
    {
        FILE *f;
        f = file_open(path, ONLY_R);
        if (NULL != f)
        {
            file_read(f, cpu_info, MAX_INFO_LEN, 0);
            fclose(f);
        }
    }

    return 0;
}
/*
 * @description   : 获取温度值
 * @param - *path : 文件路径
 * @param - *temp ：温度指针
 * @param - coef  : 温度系数
 * @return		  : 0-成功 1-失败
 */
int get_temperature_info(char *path, int *temp, int coef)
{
    if ((access(path, 0)) != -1)
    {
        FILE *f;
        f = file_open(path, ONLY_R);
        if (NULL != f)
        {
            fscanf(f, "%d", temp);
            *temp = *temp / coef;
            fclose(f);
        }
    }

    return 0;
}
/*
 * @description   : 获取系统主频
 * @param - *path : 文件路径
 * @param - *freq ：主频指针
 * @return		  : 0-成功 1-失败
 */
int get_freq_info(char *path, int *freq)
{
    if ((access(path, 0)) != -1)
    {
        FILE *f;
        f = file_open(path, ONLY_R);
        if (NULL != f)
        {
            fscanf(f, "%d", freq);
            *freq = ((*freq) / 1000);
            fclose(f);
        }
    }

    return 0;
}
/*
 * @description   : 获取cpu占用率
 * @param - *path : 文件路径
 * @param - *cpu  ：占用率指针
 * @return		  : 0-成功 1-失败
 */
int get_cpu_employ(char *path, float *cpu)
{
    struct_cpu_employ employ_value;
    char temp_line[255];
    float all = 0, used = 0;

    if ((access(path, 0)) != -1)
    {
        FILE *f;
        f = file_open(path, ONLY_R);
        if (NULL != f)
        {
            if (fgets(temp_line, sizeof(temp_line) - 1, f) != NULL)
            {
                // printf("%s\n", temp_line);
                sscanf(temp_line, "%*s %d %d %d %d %d %d %d %d %d %d",
                       &employ_value.user, &employ_value.nice, &employ_value.sys, &employ_value.idle, &employ_value.iowait,
                       &employ_value.irq, &employ_value.softirq, &employ_value.steal, &employ_value.guest, &employ_value.guest_nice);
                /*printf("explain %d,%d,%d,%d,%d,%d,%d,%d,%d,%d\n",
                       employ_value.user, employ_value.nice, employ_value.sys, employ_value.idle, employ_value.iowait,
                       employ_value.irq, employ_value.softirq, employ_value.steal, employ_value.guest, employ_value.guest_nice);*/
                all = (float)((employ_value.user - old_cpu_employ.user) + (employ_value.nice - old_cpu_employ.nice) + (employ_value.sys - old_cpu_employ.sys) + (employ_value.idle - old_cpu_employ.idle) +
                              (employ_value.iowait - old_cpu_employ.iowait) + (employ_value.irq - old_cpu_employ.irq) + (employ_value.softirq - old_cpu_employ.softirq));
                used = all - (float)(employ_value.idle - old_cpu_employ.idle);
                *cpu = ((used * 100.0) / all);
                // printf("all=%f,used=%f,employ=%f", all, used, *cpu);

                memcpy((char *)&old_cpu_employ, (char *)&employ_value, sizeof(struct_cpu_employ));
            }
            fclose(f);
        }
    }

    return 0;
}
/*
 * @description   : 获取内存占用率
 * @param - *path : 文件路径
 * @param - *cpu  ：内存占用率指针
 * @return		  : 无
 */
void get_mem_employ(char *path, float *mem, float *mem_free)
{
    long total = 0, free = 0;
    float all = 0.0f, used = 0.0f;
    char temp_line[255] = {0};

    if ((access(path, 0)) == -1)
        return;
    FILE *fp;
    sprintf(temp_line, "cat %s", path);
    fp = popen(temp_line, "r");
    if (fp != NULL)
    {
        memset(temp_line, 0, sizeof(temp_line));
        while ((fgets(temp_line, sizeof(temp_line) - 1, fp)) != NULL)
        {
            if (NULL != strstr(temp_line, "MemTotal:"))
            {
                sscanf(temp_line, "%*[^0-9]%ld", &total);
            }
            if (NULL != strstr(temp_line, "MemFree:"))
            {
                sscanf(temp_line, "%*[^0-9]%ld", &free);
            }
            if ((0 != total) && (0 != free))
            {
                break;
            }
        }
        pclose(fp);
    }

    if ((0 != total) && (0 != free))
    {
        if (NULL != mem_free)
            *mem_free = free * 1.0f;
        all = total * 1.0f;
        used = free * 1.0f;
        used = all - used;
        if (NULL != mem)
            *mem = ((used * 100.0f) / all);
    }
}
/*
 * @description                 : 获取2个温限
 * @param - *path_limit0_path   : 温限0路径
 * @param - *path_limit0_path   : 温限1路径
 * @param - *limit0             : 温限0
 * @param - *limit1             : 温限1
 * @param - coef                : 温度系数
 * @return		                : 无
 */
void get_temp_limit(char *path_limit0_path, char *path_limit1_path, int *limit0, int *limit1, int coef)
{
    FILE *f;
    if ((access(path_limit0_path, 0)) != -1)
    {
        f = file_open(path_limit0_path, ONLY_R);
        if (NULL != f)
        {
            fscanf(f, "%d", limit0);
            *limit0 = *limit0 / coef;
            fclose(f);
        }
    }
    if ((access(path_limit1_path, 0)) != -1)
    {
        f = file_open(path_limit1_path, ONLY_R);
        if (NULL != f)
        {
            fscanf(f, "%d", limit1);
            *limit1 = *limit1 / coef;
            fclose(f);
        }
    }
}
/*
 * @description          : 获取频率列表
 * @param - *list_path   : 频率列表路径
 * @param - *list_freq   : 频率列表
 * @return		         : 无
 */
void get_list_freq(char *list_path, int *list_freq)
{
    if ((access(list_path, 0)) != -1)
    {
        FILE *f;
        f = file_open(list_path, ONLY_R);
        if (NULL != f)
        {
            fscanf(f, "%d %d %d %d %d %d %d %d %d %d", &list_freq[0], &list_freq[1], &list_freq[2], &list_freq[3], &list_freq[4], &list_freq[5], &list_freq[6], &list_freq[7], &list_freq[8], &list_freq[9]);
            fclose(f);
        }
    }
}
/*
 * @description          : 获取核数
 * @param - *list_path   : 频率列表路径
 * @param - *list_freq   : 频率列表
 * @return		         : 无
 */
void get_cpus(int *cpus)
{
    *cpus = get_nprocs();
}
/*
 * @description     : 设置心跳引脚
 * @param - *path   : 路径
 * @return		    : 状态
 */
void set_heartbeat(char *path)
{
    if ((access(path, 0)) != -1)
    {
        FILE *f;
        f = fopen(path, "wt+");
        if (NULL != f)
        {
            fwrite("gpio", sizeof(char), strlen("gpio"), f); /*写入数据*/
            fflush(f);                                       /*将数据同步至ROM*/
            fclose(f);
        }
    }
}
/*
 * @description     : 控制灯亮灭
 * @param - *path   : 路径
 * @param - value   : 值
 * @return		    : 状态
 */
void conctol_heartbeat(char *path, int value)
{
    if ((access(path, 0)) != -1)
    {
        FILE *f;
        f = fopen(path, "w+");
        if (NULL == f)
            return;
        fprintf(f, "%d\n", value);
        fclose(f);
    }
}
/*
 * @description        : 获取EMMC I/O读写速率
 * @param - *dev       : EMMC设备
 * @param - *tps       : tps
 * @param - *kB_read   : 读速率
 * @param - *kB_wrtn   : 写速率
 * @return		       : 无
 */
void get_tps_r_w(char *dev, float *tps, float *kB_read, float *kB_wrtn)
{
    FILE *fstream = NULL;
    char buf[255] = {0};

    sprintf(buf, "%siostat %s 1 2", tool_path, dev);
    if (NULL == (fstream = popen(buf, "r")))
    {
        fprintf(stderr, "execute command failed: %s", strerror(errno));
    }
    else
    {
        while ((fgets(buf, sizeof(buf) - 1, fstream)) != NULL)
        {
            if (((strstr(buf, "mtdblock") != NULL)) || ((strstr(buf, "mmcblk") != NULL)))
            {
                sscanf(buf, "%*[^ ] %f %f %f", tps, kB_read, kB_wrtn);
            }
        }
        pclose(fstream);
    }
}
/*
 * @description        : 执行预执行命令
 * @param - *manag     : 测试管理结构体·
 * @return		       : 无
 */
void exe_pre_cmd(struct_test *manag)
{
    int i = 0;
    FILE *fstream = NULL;

    for (i = 0; i < manag->pre_cmd_numb; i++)
    {
        if (NULL == (fstream = popen(manag->pre_cmd[i], "r")))
        {
            fprintf(stderr, "execute command failed: %s", strerror(errno));
        }
        else
        {
            pclose(fstream);
        }
    }
}

/*
 * @description      : 自检统一打印测试结果
 * @param - *result  : PASSED FAILED
 * @param - items    : 测试项目
 * @param - *mesage  : 错误信息
 * @return		     : 无
 */
void bsp_self_check_print(char *result, char *items, char *mesage)
{
    if (strstr(result, "PASSED") != NULL)
    {
        printf(L_GREEN "%s self check is PASSED" NONE "\n", items);
    }
    else if (strstr(result, "FAILED") != NULL)
    {
        printf(L_RED "%s self check is FAILED; reason : %s" NONE "\n", items, mesage);
    }
}
/*
 * @description       : 替换命令中的变量
 * @param - *src_cmd  : 命令
 * @param - *des_cmd  : 目标命令
 * @return		      : 无
 */
void bsp_replace_times(char *src_cmd, char *des_cmd)
{
    if (NULL == src_cmd)
    {
        strcpy(des_cmd, src_cmd);
        return;
    }

    char *temp = strdup(src_cmd);
    if (temp == NULL)
    {
        strcpy(des_cmd, src_cmd);
        return;
    }

    char *ptr = temp;
    char *c_pos = NULL;

    // 查找所有"-c"实例
    if ((c_pos = strstr(ptr, "-c")) != NULL)
    {
        // 确保"-c"是一个独立的参数（前面是空格或字符串开始）
        if (c_pos == temp || isspace(*(c_pos - 1)))
        {
            // 移动到"-c"后面
            char *num_pos = c_pos + 2;

            // 跳过空格
            while (*num_pos && isspace(*num_pos))
            {
                num_pos++;
            }

            // 如果后面是数字，替换它
            if (*num_pos && isdigit(*num_pos))
            {
                char *end_num = num_pos;
                while (*end_num && isdigit(*end_num))
                {
                    end_num++;
                }

                // 替换数字为"3"
                *num_pos = '3';

                // 如果原数字长度大于1，需要移动后面的内容
                if (end_num - num_pos > 1)
                {
                    memmove(num_pos + 1, end_num, strlen(end_num) + 1);
                }
            }
        }
    }

    // 复制修改后的字符串到输出缓冲区
    strcpy(des_cmd, temp);
    free(temp);
    return;
}
/*
 * @description : 任意测试自检
 * @param -*cmd : 命令
 * @return		: 无
 */
int func_self_check_any_test(struct_test_cmd *cmd)
{
    char temp_item[100] = {0};
    char err_reson[200] = {0};
    char temp_file_name[200] = {0};
    char line[500] = {0};
    char temp_exe[200] = {0};
    FILE *tempLogFile = NULL;
    char *p = NULL;
    int have_res = 0;
    int result = 0;

    sprintf(temp_item, "%s", cmd->exe_param.dev);
    switch (cmd->exe_param.step)
    {
    case 0:
    {
        if (1 == cmd->exe_param.param_err)
        {
            cmd->state = CURRENT_STATE_OVER;
            sprintf(err_reson, "%s param err", cmd->exe_param.dev);
            bsp_self_check_print("FAILED", temp_item, err_reson);
            return -1;
        }
        if (1 == cmd->exe_param.response_flag)
        {
            cmd->state = CURRENT_STATE_OVER;
            cmd->exe_param.step = 0;
            return 2;
        }
        else
        {
            bsp_replace_times(cmd->exe, temp_exe);
            bsp_my_system(temp_exe, &(cmd->exe_param.pid));
            check_command_pid(cmd->exe_param.pid);
            cmd->exe_param.step++;
        }
    }
    break;
    case 1: //
    {
        if (0 != cmd->exe_param.pid)
        {
            if (0 == check_command_pid(cmd->exe_param.pid))
                cmd->exe_param.pid = 0;
        }
        else
        {
            // 确认测试结果
            sprintf(temp_file_name, "%s%s_temp.log", temp_path, cmd->exe_param.dev);
            tempLogFile = file_open(temp_file_name, ONLY_R);
            if (NULL == tempLogFile)
            {
                cmd->state = CURRENT_STATE_OVER;
                sprintf(err_reson, " temp file err");
                bsp_self_check_print("FAILED", temp_item, err_reson);
                return -1;
            }

            fseek(tempLogFile, 0, SEEK_SET);

            while (!feof(tempLogFile))
            {
                memset(line, 0, sizeof(line));
                if (fgets(line, sizeof(line) - 1, tempLogFile) != NULL)
                {
                    if (NULL != (strstr(line, cmd->exe_param.err_keyword0)))
                    {
                        cmd->state = CURRENT_STATE_OVER;
                        strcpy(err_reson, "exe err");
                        bsp_self_check_print("FAILED", temp_item, err_reson);
                        have_res = 1;
                        result = -1;
                        break;
                    }
                    else if (NULL != (p = strstr(line, cmd->exe_param.keyword)))
                    {
                        cmd->state = CURRENT_STATE_OVER;
                        bsp_self_check_print("PASSED", temp_item, err_reson);
                        have_res = 1;
                        result = 1;
                        break;
                    }
                }
            }
            if (!have_res)
            {
                cmd->state = CURRENT_STATE_OVER;
                strcpy(err_reson, "exe err");
                bsp_self_check_print("FAILED", temp_item, err_reson);
                result = -1;
            }
            file_close(tempLogFile);
            unlink(temp_file_name);
        }
    }
    break;
    default:
    {
        cmd->state = CURRENT_STATE_OVER;
        strcpy(err_reson, "exe err");
        bsp_self_check_print("FAILED", temp_item, err_reson);
        return -1;
    }
    }
    return result;
}
/*
 * @description : dd测试自检
 * @param -*cmd : 命令
 * @return		: 无
 */
int func_self_check_dd_test(struct_test_cmd *cmd, int point)
{
    char temp_item[100] = {0};
    char err_reson[200] = {0};
    struct stat st;
    struct statfs fs;

    sprintf(temp_item, "dd path %s ", cmd->exe_param.dd_path);
    if (1 == cmd->exe_param.param_err)
    {
        cmd->state = CURRENT_STATE_OVER;
        sprintf(err_reson, "%s param err", cmd->exe_param.dev);
        bsp_self_check_print("FAILED", temp_item, err_reson);
        return -1;
    }
    if (strlen(cmd->exe_param.dd_path) == 0)
    {
        cmd->state = CURRENT_STATE_OVER;
        sprintf(err_reson, "%s dd path not exist", cmd->exe_param.dev);
        bsp_self_check_print("FAILED", temp_item, err_reson);
        return -1;
    }

    // 检查路径是否存在
    if (stat(cmd->exe_param.dd_path, &st) != 0)
    {
        if (errno == ENOENT)
        {
            cmd->state = CURRENT_STATE_OVER;
            sprintf(err_reson, "%s dd path not exist", cmd->exe_param.dev);
            bsp_self_check_print("FAILED", temp_item, err_reson);
            return -1;
        }
    }
    else
    {
        if ((SD == point) || (USB == point))
        {
            // 路径存在，获取文件系统信息
            if (statfs(cmd->exe_param.dd_path, &fs) != 0)
            {
                cmd->state = CURRENT_STATE_OVER;
                sprintf(err_reson, "%s path get fs type fail", cmd->exe_param.dev);
                bsp_self_check_print("FAILED", temp_item, err_reson);
                return -1;
            }
            if (fs.f_type != EXT4_SUPER_MAGIC)
            {
                cmd->state = CURRENT_STATE_OVER;
                sprintf(err_reson, "%s path fs type is not etx4", cmd->exe_param.dev);
                bsp_self_check_print("FAILED", temp_item, err_reson);
                return -1;
            }
        }
    }
    cmd->state = CURRENT_STATE_OVER;
    bsp_self_check_print("PASSED", temp_item, err_reson);

    return 1;
}
/*
 * @description : net测试自检
 * @param -*cmd : 命令
 * @return		: 无
 */
int func_self_check_net_test(struct_test_cmd *cmd)
{
    char temp_item[100] = {0};
    char err_reson[200] = {0};
    char temp_file_name[200] = {0};
    char my_cmd[400] = {0};
    unsigned long curr_time;
    char line[500] = {0};
    FILE *tempLogFile = NULL;
    char *p = NULL;
    int have_res = 0;
    int result = 0;

    if (0 == cmd->exe_param.net_flag)
        sprintf(temp_item, "iperf3 %s forward", cmd->exe_param.dev);
    else
        sprintf(temp_item, "iperf3 %s reverse", cmd->exe_param.dev);
    switch (cmd->exe_param.step)
    {
    case 0:
    {
        char cmd_separate[2][200] = {0};
        sscanf(cmd->exe, "%s %s", cmd_separate[0], cmd_separate[1]);
        if (strstr(cmd_separate[1], "/sys/") != NULL)
        {
            if (func_get_netname(cmd->exe_param.dev, cmd_separate[1], cmd->exe_param.usb3_to_net_flag) < 0)
            {
                cmd->exe_param.param_err = 1;
            }
            printf("path=%s,dev=%s,vir_net=net%d\n", cmd_separate[1], cmd->exe_param.dev, cmd->exe_param.net_num);
        }
        else
            printf("dev=%s,vir_net=net%d\n", cmd->exe_param.dev, cmd->exe_param.net_num);

        if (1 == cmd->exe_param.param_err)
        {
            cmd->state = CURRENT_STATE_OVER;
            strcpy(err_reson, "iperf3 param err");
            bsp_self_check_print("FAILED", temp_item, err_reson);
            return -1;
        }
        if (1 == cmd->exe_param.response_flag)
        {
            cmd->state = CURRENT_STATE_OVER;
            cmd->exe_param.step = 0;
            return 2;
        }

        memset(my_cmd, 0, sizeof(my_cmd));
        sprintf(my_cmd, "ifconfig %s 192.168.%d.231 netmask *************", cmd->exe_param.dev, cmd->exe_param.net_num);
        bsp_my_system2(my_cmd, 'w');
        sleep(2);
        memset(my_cmd, 0, sizeof(my_cmd));
        if (0 == cmd->exe_param.net_flag)
        {
            sprintf(my_cmd, "%siperf3 -c 192.168.%d.232 -t %d 2>&1 | tee -a %snet%d_temp.log &", tool_path, cmd->exe_param.net_num, 3, temp_path, cmd->exe_param.net_num);
        }
        else
        {
            sprintf(my_cmd, "%siperf3 -c 192.168.%d.232 -R -t %d 2>&1 | tee -a %snet%d_temp.log &", tool_path, cmd->exe_param.net_num, 3, temp_path, cmd->exe_param.net_num);
        }
        bsp_my_system(my_cmd, &(cmd->exe_param.pid));
        check_command_pid(cmd->exe_param.pid);
        cmd->exe_param.start_time = func_get_system_time_ms();
        cmd->exe_param.step++;
    }
    break;
    case 1: //
    {
        curr_time = func_get_system_time_ms();
        if ((curr_time - cmd->exe_param.start_time) > (cmd->timeout * 1000))
        {
            kill_command(cmd->exe_param.pid);
            cmd->exe_param.pid = 0;
            cmd->state = CURRENT_STATE_OVER;
            strcpy(err_reson, "iperf3 test timeout");
            bsp_self_check_print("FAILED", temp_item, err_reson);
            return -1;
        }
        else
        {
            if (0 != cmd->exe_param.pid)
            {
                if (0 == check_command_pid(cmd->exe_param.pid))
                    cmd->exe_param.pid = 0;
            }
            else
            {
                // 确认测试结果
                sprintf(temp_file_name, "%snet%d_temp.log", temp_path, cmd->exe_param.net_num);
                tempLogFile = file_open(temp_file_name, ONLY_R);
                if (NULL == tempLogFile)
                {
                    cmd->state = CURRENT_STATE_OVER;
                    strcpy(err_reson, "iperf3 temp file err");
                    bsp_self_check_print("FAILED", temp_item, err_reson);
                    return -1;
                }

                fseek(tempLogFile, 0, SEEK_SET);

                while (!feof(tempLogFile))
                {
                    memset(line, 0, sizeof(line));
                    if (fgets(line, sizeof(line) - 1, tempLogFile) != NULL)
                    {
                        if ((NULL != (strstr(line, cmd->exe_param.err_keyword0))) || (NULL != (strstr(line, cmd->exe_param.err_keyword1))))
                        {
                            cmd->state = CURRENT_STATE_OVER;
                            strcpy(err_reson, "iperf3 exe err");
                            bsp_self_check_print("FAILED", temp_item, err_reson);
                            have_res = 1;
                            result = -1;
                            break;
                        }
                        else if (NULL != (p = strstr(line, cmd->exe_param.keyword)))
                        {
                            cmd->state = CURRENT_STATE_OVER;
                            bsp_self_check_print("PASSED", temp_item, err_reson);
                            have_res = 1;
                            result = 1;
                            break;
                        }
                    }
                }
                if (!have_res)
                {
                    cmd->state = CURRENT_STATE_OVER;
                    strcpy(err_reson, "iperf3 exe err");
                    bsp_self_check_print("FAILED", temp_item, err_reson);
                    result = -1;
                }
                fseek(tempLogFile, 0, SEEK_SET);
                file_close(tempLogFile);
                unlink(temp_file_name);
            }
        }
    }
    break;
    default:
    {
        cmd->state = CURRENT_STATE_OVER;
        strcpy(err_reson, "iperf3 exe err");
        bsp_self_check_print("FAILED", temp_item, err_reson);
        return -1;
    }
    }
    return result;
}
/*
 * @description : RTC模块自检
 * @param -*cmd : 命令
 * @return		: 无
 */
int func_self_check_rtc_test(struct_test_cmd *cmd)
{
    char temp_item[100] = {0};
    char err_reson[100] = {0};
    int fd;
    int ret = 0;
    struct rtc_time rtc_tm_set = {0};
    struct rtc_time rtc_tm_read = {0};

    sprintf(temp_item, "rtc test");

    if (1 == cmd->exe_param.param_err)
    {
        cmd->state = CURRENT_STATE_OVER;
        strcpy(err_reson, "test param err");
        bsp_self_check_print("FAILED", temp_item, err_reson);
        return -1;
    }
    // 打开RTC设备
    fd = open(cmd->exe_param.dev, O_RDWR | O_CLOEXEC);
    if (fd < 0)
    {
        cmd->state = CURRENT_STATE_OVER;
        strcpy(err_reson, "test rtc0 param err");
        bsp_self_check_print("FAILED", temp_item, err_reson);
        return -1;
    }
    fd = open(cmd->exe_param.dev1, O_RDWR | O_CLOEXEC);
    if (fd < 0)
    {
        cmd->state = CURRENT_STATE_OVER;
        strcpy(err_reson, "test rtc1 param err");
        bsp_self_check_print("FAILED", temp_item, err_reson);
        return -1;
    }

    // 设置RTC时间为2025年9月10日 12:00:00
    rtc_tm_set.tm_year = 2025 - 1900; // 年份需要减去1900
    rtc_tm_set.tm_mon = 9 - 1;        // 月份从0开始，所以5月是4
    rtc_tm_set.tm_mday = 1;           // 日期
    rtc_tm_set.tm_hour = 12;          // 小时
    rtc_tm_set.tm_min = 0;            // 分钟
    rtc_tm_set.tm_sec = 0;            // 秒

    printf("Setting RTC0 time to: 2025-09-10 12:00:00\n");

    // 设置RTC时间
    ret = ioctl(fd, RTC_SET_TIME, &rtc_tm_set);
    if (ret < 0)
    {
        cmd->state = CURRENT_STATE_OVER;
        strcpy(err_reson, "failed to set RTC time");
        bsp_self_check_print("FAILED", temp_item, err_reson);
        close(fd);
        return -1;
    }

    // 等待一小段时间确保设置生效
    usleep(100000); // 100ms

    // 读取RTC时间
    ret = ioctl(fd, RTC_RD_TIME, &rtc_tm_read);
    if (ret < 0)
    {
        cmd->state = CURRENT_STATE_OVER;
        strcpy(err_reson, "failed to read RTC time");
        bsp_self_check_print("FAILED", temp_item, err_reson);
        close(fd);
        return -1;
    }

    // 打印读取到的时间
    printf("Read RTC time: %04d-%02d-%02d %02d:%02d:%02d\n",
           rtc_tm_read.tm_year + 1900, rtc_tm_read.tm_mon + 1, rtc_tm_read.tm_mday,
           rtc_tm_read.tm_hour, rtc_tm_read.tm_min, rtc_tm_read.tm_sec);

    // 验证时间是否正确设置
    if (rtc_tm_read.tm_year != rtc_tm_set.tm_year ||
        rtc_tm_read.tm_mon != rtc_tm_set.tm_mon ||
        rtc_tm_read.tm_mday != rtc_tm_set.tm_mday)
    {
        cmd->state = CURRENT_STATE_OVER;
        strcpy(err_reson, "RTC Date mismatch");
        bsp_self_check_print("FAILED", temp_item, err_reson);
        close(fd);
        return -1;
    }

    cmd->state = CURRENT_STATE_OVER;
    bsp_self_check_print("PASSED", temp_item, err_reson);

    close(fd);
    return 0;
}
/*
 * @description : 检查NAND变位自检
 * @param -*cmd : 命令
 * @return		: 无
 */
int func_self_check_nand_test(struct_test_cmd *cmd)
{
    char temp_item[100] = {0};
    char err_reson[200] = {0};
    int fd = 0;
    char temp_file_name[200] = {0};

    sprintf(temp_item, "check_nand %s", cmd->exe_param.dev);
    if (1 == cmd->exe_param.param_err)
    {
        cmd->state = CURRENT_STATE_OVER;
        strcpy(err_reson, "check_nand param err");
        bsp_self_check_print("FAILED", temp_item, err_reson);
        return -1;
    }
    if ((fd = open(cmd->exe_param.dev, O_RDONLY | O_CLOEXEC)) == -1)
    {
        cmd->state = CURRENT_STATE_OVER;
        strcpy(err_reson, "can not open device");
        bsp_self_check_print("FAILED", temp_item, err_reson);
        return -1;
    }
    else
    {
        close(fd);
    }
    sprintf(temp_file_name, "%s/nand_test_file.bin", temp_path);
    // 创建1个文件，写入随机数，大小为2M
    fd = open(temp_file_name, O_RDWR | O_CREAT | O_CLOEXEC, 0666);
    if (fd < 0)
    {
        cmd->state = CURRENT_STATE_OVER;
        strcpy(err_reson, "can not create file");
        bsp_self_check_print("FAILED", temp_item, err_reson);
    }
    // 写入随机数
    srand(time(NULL));
    for (int i = 0; i < 2 * 1024 * 1024; i++)
    {
        int random_value = rand();
        write(fd, &random_value, 1);
    }
    // 关闭文件
    close(fd);
    // 获取文件md5值
    sprintf(cmd->exe_param.nand_file_md5, "%s", bsp_get_md5_value(temp_file_name));

    cmd->state = CURRENT_STATE_OVER;
    bsp_self_check_print("PASSED", temp_item, err_reson);

    return 1;
}
/*
 * @description        : 将自检结果发送到B端
 * @param - res        : 测试结果
 * @param - point      : 测试项
 * @return		       : 无
 */
void func_send_res_to_B(int res, int point, char *dev)
{
    char temp[100] = {0};
    int len = 0;
    if (res == 1)
    {
        len = sprintf(temp, "%s self check is PASSED\n", dev);
    }
    else if (res == -1)
    {
        len = sprintf(temp, "%s self check is FAILED\n", dev);
    }
    if ((CAN == point) || (RS485 == point) || (RS232 == point) || (NET == point))
        func_tcp_send(sockfd, temp, len);
}
/*
 * @description        : 执行自检命令
 * @param - *manag     : 测试管理结构体·
 * @return		       : 无
 */
int func_self_check(struct_test *manag)
{
    int i = 0, j = 0;
    int ret = 0;
    int point = 0;
    int total_result = 0;
    int flag = 0;

    for (point = 0; point < MAX_TEST_ITEM; point++)
    {
        if (manag->item[point].circle != 0)
        {
            for (i = 0; i < manag->item[point].groups; i++)
            {
                for (j = 0; j < manag->item[point].group[i].numb; j++)
                {
                    manag->item[point].group[i].cmd[j].state = CURRENT_STATE_RUN;
                    manag->item[point].group[i].cmd[j].exe_param.step = 0;
                    manag->item[point].group[i].cmd[j].exe_param.pid = 0;
                }
            }
            for (i = 0; i < manag->item[point].groups; i++)
            {
                while (1)
                {
                    for (j = 0; j < manag->item[point].group[i].numb; j++)
                    {
                        if (CURRENT_STATE_RUN == manag->item[point].group[i].cmd[j].state)
                        {
                            switch (manag->item[point].group[i].cmd[j].exe_param.mode)
                            {
                            case ANY_MODE:
                            {
                                ret = func_self_check_any_test(&(manag->item[point].group[i].cmd[j]));
                                if (-1 == ret)
                                    total_result = -1;
                                if ((1 == manag->interact_flag) && ((1 == ret) || (-1 == ret)))
                                    func_send_res_to_B(ret, point, manag->item[point].group[i].cmd[j].exe_param.dev);
                            }
                            break;
                            case DD_MODE: //
                            {
                                ret = func_self_check_dd_test(&(manag->item[point].group[i].cmd[j]), point);
                                if (-1 == ret)
                                    total_result = -1;
                            }
                            break;
                            case MEMTER_MODE: //
                            {
                                if (0 == j)
                                {
                                    bsp_self_check_print("PASSED", "memory", NULL);
                                }
                                manag->item[point].group[i].cmd[j].state = CURRENT_STATE_OVER;
                            }
                            break;
                            case MEM_BW_MODE: //
                            {
                                manag->item[point].group[i].cmd[j].state = CURRENT_STATE_OVER;
                            }
                            break;
                            case RTC_TEST_MODE: //
                            {
                                ret = func_self_check_rtc_test(&(manag->item[point].group[i].cmd[j]));
                                if (-1 == ret)
                                    total_result = -1;
                            }
                            break;
                            case CHECK_NAND_ERR: //
                            {
                                ret = func_self_check_nand_test(&(manag->item[point].group[i].cmd[j]));
                                if (-1 == ret)
                                    total_result = -1;
                            }
                            break;
                            case NET_MODE: //
                            {
                                ret = func_self_check_net_test(&(manag->item[point].group[i].cmd[j]));
                                if (-1 == ret)
                                    total_result = -1;
                                if ((1 == manag->interact_flag) && ((1 == ret) || (-1 == ret)))
                                    func_send_res_to_B(ret, point, manag->item[point].group[i].cmd[j].exe_param.dev);
                            }
                            break;
                            default:
                            {
                                ret = func_self_check_any_test(&(manag->item[point].group[i].cmd[j]));
                                if (-1 == ret)
                                    total_result = -1;
                                if ((1 == manag->interact_flag) && ((1 == ret) || (-1 == ret)))
                                    func_send_res_to_B(ret, point, manag->item[point].group[i].cmd[j].exe_param.dev);
                            }
                            }
                        }
                    }
                    flag = 0;
                    for (j = 0; j < manag->item[point].group[i].numb; j++)
                    {
                        if (CURRENT_STATE_RUN == manag->item[point].group[i].cmd[j].state)
                        {
                            flag = 1;
                            break;
                        }
                    }
                    if (0 == flag)
                    {
                        break;
                    }
                    usleep(3000);
                }
            }
        }
    }
    if (manag->interact_flag == 1)
    {
        if (-1 == total_result)
        {
            printf(L_RED "*** self check is end,result is FAILED ! ! !; ***" NONE "\n");
            func_tcp_send(sockfd, "self check is end,result is FAILED\n", strlen("self check is end,result is FAILED\n"));
        }
        else
        {
            printf(L_GREEN "*** self check is end,result is PASSED ***" NONE "\n");
            // 自检完成，发送自检完成信号
            if (manag->pattern == SPONSOR_IN_ORDER)
                func_tcp_send(sockfd, "start test", strlen("start test"));
        }
    }
    else
    {
        if (-1 == total_result)
        {
            printf(L_RED "*** self check is end,result is FAILED ! ! !; ***" NONE "\n");
        }
        else
        {
            printf(L_GREEN "*** self check is end,result is PASSED ***" NONE "\n");
        }
    }
    for (point = 0; point < MAX_TEST_ITEM; point++)
    {
        if (manag->item[point].circle != 0)
        {
            for (i = 0; i < manag->item[point].groups; i++)
            {
                for (j = 0; j < manag->item[point].group[i].numb; j++)
                {
                    manag->item[point].group[i].cmd[j].state = CURRENT_STATE_RUN;
                    manag->item[point].group[i].cmd[j].exe_param.step = 0;
                    manag->item[point].group[i].cmd[j].exe_param.pid = 0;
                }
            }
        }
    }

    return total_result;
}