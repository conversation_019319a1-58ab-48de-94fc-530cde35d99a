
#ifndef NET_MANAGE_H
#define NET_MANAGE_H

#define CONNECT 1
#define DISCONNECT 0

#define CREATE_TURE 1
#define CREATE_FALSE 0

typedef enum
{
    NET_QUITE_EVENT = 1,  /*线程退出主线程让网络线程退出*/
    NET_CONNECT_OK = 2,   /*连接成功*/
    NET_CONNECT_FAIL = 3, /*连接失败*/
    NET_THREAD_QUITE = 4, /*线程退出，网络线程主动退出*/
    NET_SEND_FAIL = 5,    /*发送失败*/

    NET_RX_START_SELF_CHECK = 6, /*自检开始*/
    NET_RX_CHECK_FAIL = 7,       /*自检失败*/
    NET_RX_START_TEST = 8,       /*测试开始*/
    NET_RX_TEST_NEXT = 9,        /*测试下一轮*/

} enum_net_socketpair_type;

extern int sockfd;
extern int listenfd;
extern int net_inter_signal_fd[2];

int func_tcp_send(int fd, char *p_send_buff, int count);
void *net_client_manage(void *arg);
void *net_server_manage(void *arg);
void net_inter_send_event_to_main(int event);
#endif