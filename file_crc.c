#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>

/* CRC-32 多项式表 */
static unsigned int crc32_table[256];

/**
 * @description    : 初始化CRC-32表
 * @return         : 无
 */
void init_crc32_table(void)
{
    unsigned int c;
    int i, j;
    
    for (i = 0; i < 256; i++) {
        c = (unsigned int)i;
        for (j = 0; j < 8; j++) {
            if (c & 1)
                c = 0xEDB88320 ^ (c >> 1);
            else
                c = c >> 1;
        }
        crc32_table[i] = c;
    }
}

/**
 * @description    : 计算缓冲区的CRC-32值
 * @param - buf    : 数据缓冲区
 * @param - len    : 缓冲区长度
 * @param - crc    : 初始CRC值
 * @return         : 计算得到的CRC-32值
 */
unsigned int calculate_crc32(const unsigned char *buf, size_t len, unsigned int crc)
{
    unsigned int i;
    
    crc = ~crc;
    for (i = 0; i < len; i++) {
        crc = crc32_table[(crc ^ buf[i]) & 0xFF] ^ (crc >> 8);
    }
    return ~crc;
}

/**
 * @description    : 计算文件的CRC-32值
 * @param - path   : 文件路径
 * @return         : 成功返回CRC-32值，失败返回0
 */
unsigned int calculate_file_crc32(const char *path)
{
    FILE *file;
    unsigned char buffer[1024];
    size_t bytes_read;
    unsigned int crc = 0;
    
    /* 打开文件 */
    file = fopen(path, "rb");
    if (file == NULL) {
        printf("无法打开文件: %s\n", path);
        return 0;
    }
    
    /* 初始化CRC值 */
    crc = 0;
    
    /* 读取文件内容并计算CRC */
    while ((bytes_read = fread(buffer, 1, sizeof(buffer), file)) > 0) {
        crc = calculate_crc32(buffer, bytes_read, crc);
    }
    
    /* 关闭文件 */
    fclose(file);
    
    return crc;
}

/**
 * @description    : 验证文件的CRC-32值
 * @param - path   : 文件路径
 * @param - expected_crc: 期望的CRC-32值
 * @return         : 验证成功返回1，失败返回0
 */
int verify_file_crc32(const char *path, unsigned int expected_crc)
{
    unsigned int actual_crc;
    
    /* 计算文件的实际CRC值 */
    actual_crc = calculate_file_crc32(path);
    if (actual_crc == 0) {
        return 0; /* 文件打开失败 */
    }
    
    /* 比较CRC值 */
    if (actual_crc == expected_crc) {
        printf("CRC校验成功: 0x%08X\n", actual_crc);
        return 1;
    } else {
        printf("CRC校验失败!\n");
        printf("期望的CRC: 0x%08X\n", expected_crc);
        printf("实际的CRC: 0x%08X\n", actual_crc);
        return 0;
    }
}

/**
 * @description    : 保存文件的CRC-32值到另一个文件
 * @param - path   : 文件路径
 * @param - crc_path: CRC文件路径
 * @return         : 成功返回1，失败返回0
 */
int save_file_crc32(const char *path, const char *crc_path)
{
    FILE *crc_file;
    unsigned int crc;
    
    /* 计算文件CRC */
    crc = calculate_file_crc32(path);
    if (crc == 0) {
        return 0;
    }
    
    /* 保存CRC到文件 */
    crc_file = fopen(crc_path, "wb");
    if (crc_file == NULL) {
        printf("无法创建CRC文件: %s\n", crc_path);
        return 0;
    }
    
    fprintf(crc_file, "%08X", crc);
    fclose(crc_file);
    
    printf("文件 %s 的CRC-32值 (0x%08X) 已保存到 %s\n", path, crc, crc_path);
    return 1;
}

/**
 * @description    : 从CRC文件加载期望的CRC-32值
 * @param - crc_path: CRC文件路径
 * @return         : 成功返回CRC-32值，失败返回0
 */
unsigned int load_expected_crc32(const char *crc_path)
{
    FILE *crc_file;
    unsigned int crc = 0;
    char crc_str[9] = {0};
    
    /* 打开CRC文件 */
    crc_file = fopen(crc_path, "rb");
    if (crc_file == NULL) {
        printf("无法打开CRC文件: %s\n", crc_path);
        return 0;
    }
    
    /* 读取CRC值 */
    if (fread(crc_str, 1, 8, crc_file) != 8) {
        printf("读取CRC文件失败\n");
        fclose(crc_file);
        return 0;
    }
    
    fclose(crc_file);
    
    /* 转换CRC字符串为整数 */
    sscanf(crc_str, "%x", &crc);
    
    return crc;
}

/**
 * @description    : 使用示例
 */
int main(int argc, char *argv[])
{
    const char *file_path;
    const char *crc_path;
    unsigned int expected_crc;
    
    /* 初始化CRC表 */
    init_crc32_table();
    
    if (argc < 2) {
        printf("用法:\n");
        printf("  计算CRC: %s -c <文件路径>\n", argv[0]);
        printf("  保存CRC: %s -s <文件路径> <CRC文件路径>\n", argv[0]);
        printf("  验证CRC: %s -v <文件路径> <CRC文件路径>\n", argv[0]);
        return 1;
    }
    
    /* 计算文件CRC */
    if (strcmp(argv[1], "-c") == 0) {
        if (argc < 3) {
            printf("错误: 缺少文件路径\n");
            return 1;
        }
        
        file_path = argv[2];
        unsigned int crc = calculate_file_crc32(file_path);
        if (crc != 0) {
            printf("文件 %s 的CRC-32值: 0x%08X\n", file_path, crc);
        }
    }
    /* 保存文件CRC到另一个文件 */
    else if (strcmp(argv[1], "-s") == 0) {
        if (argc < 4) {
            printf("错误: 缺少参数\n");
            return 1;
        }
        
        file_path = argv[2];
        crc_path = argv[3];
        save_file_crc32(file_path, crc_path);
    }
    /* 验证文件CRC */
    else if (strcmp(argv[1], "-v") == 0) {
        if (argc < 4) {
            printf("错误: 缺少参数\n");
            return 1;
        }
        
        file_path = argv[2];
        crc_path = argv[3];
        
        expected_crc = load_expected_crc32(crc_path);
        if (expected_crc != 0) {
            verify_file_crc32(file_path, expected_crc);
        }
    }
    else {
        printf("未知选项: %s\n", argv[1]);
        return 1;
    }
    
    return 0;
} 