
#ifndef CONFIG_FILE_H
#define CONFIG_FILE_H

#include "manage.h"

#define MAX_CONFIG_BUF_SIZE 1024
#define MAX_VALUE_SIZE 512
#define MAX_CONFIG_ITEM 35

#define CHAR_MODE 0
#define INT_MODE 1

typedef struct
{
    char key[50]; /* conf keyword */
    int type;
    char value[MAX_VALUE_SIZE];
    char have;
} conf_s;
typedef struct
{
    const char key[50]; /* conf keyword */
    const int type;
    int value;
} conf_d;

int func_get_conf(struct_test *manag, char *path);
#endif